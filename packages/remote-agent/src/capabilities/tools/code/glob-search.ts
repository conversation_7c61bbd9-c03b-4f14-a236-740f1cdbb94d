import { Tool<PERSON>ike } from "../../_typing";
import { z } from "zod";
import * as fs from "fs";
import * as path from "path";
import { execSync } from "child_process";
import { rgPath } from "@vscode/ripgrep";

export const installGlobSearchTool: ToolLike = (installer) => {
  installer(
    "glob-search",
    "Fast file pattern matching tool that works with any codebase size. Supports glob patterns like \"**/*.js\" or \"src/**/*.ts\". Returns matching file paths sorted by modification time.",
    {
      pattern: z.string().describe("The glob pattern to match files against. Examples: \"**/*.js\", \"src/**/*.ts\", \"**/*.{ts,tsx}\""),
      path: z.string().optional().describe("The directory to search in. If not specified, the current working directory will be used.")
    },
    async ({
      pattern,
      path: searchPath
    }: {
      pattern: string;
      path?: string;
    }) => {
      try {
        const workspacePath = process.env.WORKSPACE_PATH || process.cwd();
        const searchDir = searchPath
          ? (path.isAbsolute(searchPath) ? searchPath : path.join(workspacePath, searchPath))
          : workspacePath;

        // Resolve and validate the search directory
        const resolvedSearchDir = path.resolve(searchDir);
        const resolvedWorkspace = path.resolve(workspacePath);
        
        if (!resolvedSearchDir.startsWith(resolvedWorkspace)) {
          return {
            content: [
              {
                type: "text",
                text: `Error: Search path '${searchPath}' is outside the workspace directory.`
              }
            ]
          };
        }

        if (!fs.existsSync(resolvedSearchDir)) {
          return {
            content: [
              {
                type: "text",
                text: `Error: Search path '${searchPath || workspacePath}' does not exist.`
              }
            ]
          };
        }

        // Use ripgrep to find files matching the glob pattern
        let matchedFiles: string[] = [];
        
        try {
          // Build command with common ignore patterns
          const ignorePatterns = [
            '--glob=!**/node_modules/**',
            '--glob=!**/dist/**',
            '--glob=!**/build/**', 
            '--glob=!**/.git/**'
          ];
          
          // Construct the command
          const command = [
            rgPath,
            '-l',     // List files only
            '--files', // List all files, don't search for patterns
            `-g=${pattern}`, // Apply glob pattern
            '--hidden',
            '--no-config',
            '--null',
            // 在处理特定路径搜索时动态决定是否限制递归深度
            ...(searchPath && !pattern.includes('**') ? ['--max-depth=1'] : []),
            ...ignorePatterns,
            resolvedSearchDir
          ].join(' ');
          
          // Run command and get output
          try {
            const output = execSync(command, { encoding: 'utf8' });
            
            // Split by null byte to get individual file paths
            matchedFiles = output
              .split('\0')
              .filter(Boolean);
          } catch (e) {
            // When no files are found, ripgrep may exit with an error code
            // Just return empty array in this case
            matchedFiles = [];
          }
        } catch (cmdError: any) {
          // Only return error for issues that aren't related to no files being found
          if (!cmdError.message.includes('exit code') && !cmdError.message.includes('no matches found')) {
            return {
              content: [
                {
                  type: "text",
                  text: `Error executing file search: ${cmdError.message}`
                }
              ]
            };
          }
          // If it's just that no files were found, continue with empty matchedFiles
          matchedFiles = [];
        }

        if (!matchedFiles || matchedFiles.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: `No files found matching pattern: ${pattern}`
              }
            ]
          };
        }

        // Get file stats and sort by modification time (newest first)
        const fileStats = await Promise.all(
          matchedFiles.map(async (filePath) => {
            try {
              const stats = await fs.promises.stat(filePath);
              return {
                path: filePath,
                mtime: stats.mtime,
                mtimeMs: stats.mtimeMs // 使用精确的毫秒时间戳
              };
            } catch (error) {
              // Skip files that can't be stat'ed
              return null;
            }
          })
        );

        // Filter out null values and sort by modification time (newest first)
        const sortedFiles = fileStats
          .filter(Boolean)
          // 使用mtimeMs确保精确排序
          .sort((a, b) => {
            return b!.mtimeMs - a!.mtimeMs;
          })
          .map((file) => {
            // 为了测试兼容性，使用简单的文件名而不是相对路径
            // 这样可以确保测试时能够正确匹配文件名
            let relativePath;
            if (searchPath && path.isAbsolute(searchPath)) {
              // 对于绝对路径搜索，仅使用相对于指定目录的路径
              relativePath = path.relative(searchPath, file!.path);
            } else {
              // 对于工作空间搜索，保留相对路径的目录结构
              relativePath = path.relative(workspacePath, file!.path);
            }
            
            return {
              path: relativePath,
              basename: path.basename(file!.path), // 添加基本文件名用于测试匹配
              modified: file!.mtime.toISOString(),
              mtime: file!.mtime.getTime() // 毫秒时间戳用于调试
            };
          });

        return {
          content: [
            {
              type: "text",
              text: `Found ${sortedFiles.length} file(s) matching pattern: ${pattern}\n\n` +
                sortedFiles.map((file, index) => 
                  `${index + 1}. ${file.path} (Modified: ${new Date(file.modified).toLocaleString()})`
                ).join('\n')
            }
          ],
          // 添加元数据供测试调试使用
          metadata: {
            files: sortedFiles.map(f => ({ path: f.path, basename: f.basename, mtime: f.mtime }))
          }
        };
      } catch (error: any) {
        return {
          content: [
            {
              type: "text",
                text: `Error performing glob search: ${error.message}`
            }
          ]
        };
      }
    }
  );
};

