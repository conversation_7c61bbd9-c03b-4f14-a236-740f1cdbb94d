{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./lib", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitOverride": false, "exactOptionalPropertyTypes": false}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "test", "**/*.test.ts", "**/*.spec.ts"]}