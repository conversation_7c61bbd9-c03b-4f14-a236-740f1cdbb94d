"""
Document analyser interface and data structures
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from dataclasses import dataclass


@dataclass
class CodeDocument:
    """
    Represents a code document extracted from documentation

    Contains information about code blocks found in documentation files,
    including their language, content, and context.
    """

    # Title of the code document
    title: str

    # Programming language of the code
    language: str

    # The heading title before the code block
    last_title: str

    # Lines before the code block (for context)
    before_string: str

    # Lines after the code block (for context)
    after_string: str

    # The actual code content
    code: str

    # Start position in the original text (optional)
    start_index: Optional[int] = None

    # End position in the original text (optional)
    end_index: Optional[int] = None


class DocumentAnalyser(ABC):
    """Abstract base class for document analysers"""

    @abstractmethod
    async def parse(self, content: str) -> List[CodeDocument]:
        """
        Parse document content and extract code documents

        Args:
            content: The document content to parse

        Returns:
            List of CodeDocument objects found in the content
        """
        pass