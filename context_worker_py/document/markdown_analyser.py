"""
Markdown document analyser for extracting code blocks
"""

import re
from typing import List, Optional, Tuple
from .document_analyser import DocumentAnalys<PERSON>, CodeDocument


class MarkdownAnalyser(DocumentAnalyser):
    """
    Analyser for Markdown documents to extract code blocks

    Parses Markdown content and extracts fenced code blocks with their
    language information and surrounding context.
    """

    def __init__(self):
        # Regex pattern for fenced code blocks
        self.code_block_pattern = re.compile(
            r'```(\w+)?\s*\n(.*?)\n```',
            re.DOTALL | re.MULTILINE
        )

        # Pattern for headings
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)

    async def parse(self, content: str) -> List[CodeDocument]:
        """
        Parse Markdown content and extract code documents

        Args:
            content: The Markdown content to parse

        Returns:
            List of CodeDocument objects found in the content
        """
        code_documents = []

        # Find all headings first to provide context
        headings = self._extract_headings(content)

        # Find all code blocks
        for match in self.code_block_pattern.finditer(content):
            language = match.group(1) or 'text'
            code = match.group(2).strip()
            start_pos = match.start()
            end_pos = match.end()

            # Skip empty code blocks
            if not code:
                continue

            # Find the most recent heading before this code block
            last_title = self._find_preceding_heading(headings, start_pos)

            # Extract context before and after the code block
            before_context = self._extract_before_context(content, start_pos)
            after_context = self._extract_after_context(content, end_pos)

            # Create code document
            code_doc = CodeDocument(
                title=last_title or "Untitled",
                language=language.lower(),
                last_title=last_title or "",
                before_string=before_context,
                after_string=after_context,
                code=code,
                start_index=start_pos,
                end_index=end_pos
            )

            code_documents.append(code_doc)

        return code_documents

    def _extract_headings(self, content: str) -> List[Tuple[int, str, str]]:
        """
        Extract all headings from the content

        Returns:
            List of tuples (position, level, title)
        """
        headings = []

        for match in self.heading_pattern.finditer(content):
            level = match.group(1)  # Number of # characters
            title = match.group(2).strip()
            position = match.start()
            headings.append((position, level, title))

        return headings

    def _find_preceding_heading(self, headings: List[Tuple[int, str, str]],
                               position: int) -> Optional[str]:
        """
        Find the most recent heading before the given position

        Args:
            headings: List of (position, level, title) tuples
            position: Position to search before

        Returns:
            The title of the most recent heading, or None
        """
        last_heading = None

        for heading_pos, level, title in headings:
            if heading_pos < position:
                last_heading = title
            else:
                break

        return last_heading

    def _extract_before_context(self, content: str, start_pos: int,
                               max_lines: int = 3) -> str:
        """
        Extract context lines before the code block

        Args:
            content: Full content
            start_pos: Start position of the code block
            max_lines: Maximum number of lines to extract

        Returns:
            Context string before the code block
        """
        # Find the start of the line containing start_pos
        line_start = content.rfind('\n', 0, start_pos)
        if line_start == -1:
            line_start = 0
        else:
            line_start += 1

        # Extract text before the code block
        before_text = content[:line_start].rstrip()

        # Get the last few lines
        lines = before_text.split('\n')
        context_lines = lines[-max_lines:] if len(lines) > max_lines else lines

        return '\n'.join(context_lines).strip()

    def _extract_after_context(self, content: str, end_pos: int,
                              max_lines: int = 3) -> str:
        """
        Extract context lines after the code block

        Args:
            content: Full content
            end_pos: End position of the code block
            max_lines: Maximum number of lines to extract

        Returns:
            Context string after the code block
        """
        # Find the end of the line containing end_pos
        line_end = content.find('\n', end_pos)
        if line_end == -1:
            line_end = len(content)

        # Extract text after the code block
        after_text = content[line_end:].lstrip('\n')

        # Get the first few lines
        lines = after_text.split('\n')
        context_lines = lines[:max_lines] if len(lines) > max_lines else lines

        return '\n'.join(context_lines).strip()