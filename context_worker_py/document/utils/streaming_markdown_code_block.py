"""
Streaming Markdown code block parser for real-time processing
"""

import re
from typing import List, Optional
from dataclasses import dataclass


@dataclass
class MarkdownCodeBlock:
    """Represents a Markdown code block with position information"""
    language: str
    start_line: int
    end_line: int
    code: str


class StreamingMarkdownCodeBlock:
    """
    FencedCodeBlock class represents a block of code that is delimited by triple backticks (```)
    and can optionally have a language identifier.

    This class is used to parse and represent code blocks within Markdown or other text documents.
    It provides methods to parse the content of a code block and to retrieve the language identifier
    and the code itself.
    """

    def __init__(self, language: str, text: str, is_complete: bool,
                 start_index: int = 0, end_index: int = 0):
        self.language = language
        self.text = text
        self.is_complete = is_complete
        self.start_index = start_index
        self.end_index = end_index

    @staticmethod
    def parse(content: str, default_language: str = "") -> 'StreamingMarkdownCodeBlock':
        """Parse content and return a StreamingMarkdownCodeBlock"""
        return StreamingMarkdownCodeBlock.multi_line_code_block(content, default_language)

    @staticmethod
    def multi_line_code_block(content: str, primary_language: str = "") -> 'StreamingMarkdownCodeBlock':
        """
        Parse multi-line code blocks from content

        Args:
            content: The content to parse
            primary_language: Default language if none specified

        Returns:
            StreamingMarkdownCodeBlock instance
        """
        regex = re.compile(r'```([\w#+]*?)\s*$')
        lines = content.replace('\\n', '\n').split('\n')

        last_block_start_index = 0
        code_blocks: List[MarkdownCodeBlock] = []
        language = "markdown"
        block_content: List[str] = []

        in_code_block = False
        code_start_line = 0

        for i, line in enumerate(lines):
            if not in_code_block:
                match = regex.match(line.strip())
                if match:
                    language = match.group(1) or primary_language or "text"
                    in_code_block = True
                    code_start_line = i
                    block_content = []
            else:
                if line.strip().startswith('```'):
                    # End of code block
                    code_blocks.append(MarkdownCodeBlock(
                        language=language,
                        start_line=code_start_line,
                        end_line=i,
                        code='\n'.join(block_content)
                    ))
                    in_code_block = False
                    last_block_start_index = i + 1
                else:
                    block_content.append(line)

        # Handle incomplete code block
        if in_code_block:
            return StreamingMarkdownCodeBlock(
                language=language,
                text='\n'.join(block_content),
                is_complete=False,
                start_index=code_start_line,
                end_index=len(lines) - 1
            )

        # Return the last complete code block or markdown content
        if code_blocks:
            last_block = code_blocks[-1]
            return StreamingMarkdownCodeBlock(
                language=last_block.language,
                text=last_block.code,
                is_complete=True,
                start_index=last_block.start_line,
                end_index=last_block.end_line
            )

        # No code blocks found, return as markdown
        return StreamingMarkdownCodeBlock(
            language="markdown",
            text=content.replace('\\n', '\n'),
            is_complete=True,
            start_index=0,
            end_index=len(lines) - 1
        )

    @staticmethod
    def single_block(content: str) -> 'StreamingMarkdownCodeBlock':
        """
        Parse a single code block from content

        Args:
            content: The content to parse

        Returns:
            StreamingMarkdownCodeBlock instance
        """
        regex = re.compile(r'```([\w#+]*)')
        lines = content.replace('\\n', '\n').split('\n')

        code_started = False
        code_closed = False
        language_id: Optional[str] = None
        code_builder: List[str] = []

        for line in lines:
            if not code_started:
                match_result = regex.search(line.strip())
                if match_result:
                    language_id = match_result.group(1)
                    code_started = True
            elif line.startswith('```'):
                code_closed = True
                break
            else:
                code_builder.append(line)

        # Trim empty lines from start and end
        start_index = 0
        end_index = len(code_builder) - 1

        while start_index <= end_index:
            if not code_builder[start_index].strip():
                start_index += 1
            else:
                break

        while end_index >= start_index:
            if not code_builder[end_index].strip():
                end_index -= 1
            else:
                break

        trimmed_code = '\n'.join(code_builder[start_index:end_index + 1])
        language = language_id or "plaintext"

        # If content is not empty, but code is empty, then it's markdown
        if not trimmed_code.strip():
            return StreamingMarkdownCodeBlock(
                language="markdown",
                text=content.replace('\\n', '\n'),
                is_complete=code_closed,
                start_index=start_index,
                end_index=end_index
            )

        return StreamingMarkdownCodeBlock(
            language=language,
            text=trimmed_code,
            is_complete=code_closed,
            start_index=start_index,
            end_index=end_index
        )

    @staticmethod
    def from_markdown(markdown: str) -> List[MarkdownCodeBlock]:
        """
        Extract all code blocks from markdown content

        Args:
            markdown: The markdown content to parse

        Returns:
            List of MarkdownCodeBlock objects
        """
        regex = re.compile(r'```(\w+)?\s*([\s\S]*?)```', re.MULTILINE)
        blocks: List[MarkdownCodeBlock] = []

        for match in regex.finditer(markdown):
            language = match.group(1) or "plaintext"
            code = match.group(2).strip()

            # Calculate line numbers
            start_line = markdown[:match.start()].count('\n') + 1
            end_line = start_line + match.group(0).count('\n')

            blocks.append(MarkdownCodeBlock(
                language=language,
                start_line=start_line,
                end_line=end_line,
                code=code
            ))

        return blocks