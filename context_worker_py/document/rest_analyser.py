"""
RestructuredText (ReST) document analyser for extracting code blocks
"""

import re
from typing import List, Optional, Tuple
from .document_analyser import DocumentAnalyser, CodeDocument


class ReSTAnalyser(DocumentAnalyser):
    """
    Analyser for RestructuredText documents to extract code blocks

    Parses ReST content and extracts code-block directives with their
    language information and surrounding context.
    """

    def __init__(self):
        # Pattern for code-block directive
        self.code_block_pattern = re.compile(
            r'^\.\. code-block::\s*(\w+)?\s*\n((?:\s{4}.*\n?)*)',
            re.MULTILINE
        )

        # Pattern for section headers (underlined text)
        self.header_patterns = [
            re.compile(r'^(.+)\n={3,}$', re.MULTILINE),  # = underline
            re.compile(r'^(.+)\n-{3,}$', re.MULTILINE),  # - underline
            re.compile(r'^(.+)\n~{3,}$', re.MULTILINE),  # ~ underline
            re.compile(r'^(.+)\n\^{3,}$', re.MULTILINE), # ^ underline
            re.compile(r'^(.+)\n"{3,}$', re.MULTILINE),  # " underline
        ]

    async def parse(self, content: str) -> List[CodeDocument]:
        """
        Parse ReST content and extract code documents

        Args:
            content: The ReST content to parse

        Returns:
            List of CodeDocument objects found in the content
        """
        code_documents = []

        # Find all headings first to provide context
        headings = self._extract_headings(content)

        # Find all code blocks
        for match in self.code_block_pattern.finditer(content):
            language = match.group(1) or 'text'
            code_block = match.group(2)
            start_pos = match.start()
            end_pos = match.end()

            # Process the indented code block
            code = self._process_indented_code(code_block)

            # Skip empty code blocks
            if not code.strip():
                continue

            # Find the most recent heading before this code block
            last_title = self._find_preceding_heading(headings, start_pos)

            # Extract context before and after the code block
            before_context = self._extract_before_context(content, start_pos)
            after_context = self._extract_after_context(content, end_pos)

            # Create code document
            code_doc = CodeDocument(
                title=f"Code block at line {self._get_line_number(content, start_pos)}",
                language=language.lower(),
                last_title=last_title or "",
                before_string=before_context,
                after_string=after_context,
                code=code,
                start_index=start_pos,
                end_index=end_pos
            )

            code_documents.append(code_doc)

        return code_documents

    def _extract_headings(self, content: str) -> List[Tuple[int, str]]:
        """
        Extract all headings from the content

        Returns:
            List of tuples (position, title)
        """
        headings = []

        for pattern in self.header_patterns:
            for match in pattern.finditer(content):
                title = match.group(1).strip()
                position = match.start()
                headings.append((position, title))

        # Sort by position
        headings.sort(key=lambda x: x[0])

        return headings

    def _find_preceding_heading(self, headings: List[Tuple[int, str]],
                               position: int) -> Optional[str]:
        """
        Find the most recent heading before the given position

        Args:
            headings: List of (position, title) tuples
            position: Position to search before

        Returns:
            The title of the most recent heading, or None
        """
        last_heading = None

        for heading_pos, title in headings:
            if heading_pos < position:
                last_heading = title
            else:
                break

        return last_heading

    def _process_indented_code(self, code_block: str) -> str:
        """
        Process indented code block by removing common indentation

        Args:
            code_block: Raw indented code block

        Returns:
            Processed code with indentation removed
        """
        lines = code_block.split('\n')

        # Remove empty lines at the beginning and end
        while lines and not lines[0].strip():
            lines.pop(0)
        while lines and not lines[-1].strip():
            lines.pop()

        if not lines:
            return ""

        # Find minimum indentation (excluding empty lines)
        min_indent = float('inf')
        for line in lines:
            if line.strip():  # Skip empty lines
                indent = len(line) - len(line.lstrip())
                min_indent = min(min_indent, indent)

        if min_indent == float('inf'):
            min_indent = 0

        # Remove common indentation
        processed_lines = []
        for line in lines:
            if line.strip():  # Non-empty line
                processed_lines.append(line[min_indent:])
            else:  # Empty line
                processed_lines.append("")

        return '\n'.join(processed_lines)

    def _get_line_number(self, content: str, position: int) -> int:
        """Get line number for a given position in the content"""
        return content[:position].count('\n') + 1

    def _extract_before_context(self, content: str, start_pos: int,
                               max_lines: int = 3) -> str:
        """
        Extract context lines before the code block

        Args:
            content: Full content
            start_pos: Start position of the code block
            max_lines: Maximum number of lines to extract

        Returns:
            Context string before the code block
        """
        # Find the start of the line containing start_pos
        line_start = content.rfind('\n', 0, start_pos)
        if line_start == -1:
            line_start = 0
        else:
            line_start += 1

        # Extract text before the code block
        before_text = content[:line_start].rstrip()

        # Get the last few lines
        lines = before_text.split('\n')
        context_lines = lines[-max_lines:] if len(lines) > max_lines else lines

        return '\n'.join(context_lines).strip()

    def _extract_after_context(self, content: str, end_pos: int,
                              max_lines: int = 3) -> str:
        """
        Extract context lines after the code block

        Args:
            content: Full content
            end_pos: End position of the code block
            max_lines: Maximum number of lines to extract

        Returns:
            Context string after the code block
        """
        # Find the end of the line containing end_pos
        line_end = content.find('\n', end_pos)
        if line_end == -1:
            line_end = len(content)

        # Extract text after the code block
        after_text = content[line_end:].lstrip('\n')

        # Get the first few lines
        lines = after_text.split('\n')
        context_lines = lines[:max_lines] if len(lines) > max_lines else lines

        return '\n'.join(context_lines).strip()