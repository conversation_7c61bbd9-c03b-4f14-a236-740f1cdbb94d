"""
Code element type definitions
"""

from enum import Enum


class CodeElementType(Enum):
    """Types of code elements that can be identified and analyzed"""

    FILE = "file"
    STRUCTURE = "structure"  # Class, interface, etc.
    METHOD = "method"
    FUNCTION = "function"
    VARIABLE = "variable"

    def __str__(self) -> str:
        return self.value

    def __repr__(self) -> str:
        return f"CodeElementType.{self.name}"