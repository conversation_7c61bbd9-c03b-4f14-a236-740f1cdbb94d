"""
Position element definitions for code elements
"""

from dataclasses import dataclass
from typing import Protocol


@dataclass
class CodePosition:
    """Represents a position in source code"""
    row: int
    column: int

    def __str__(self) -> str:
        return f"{self.row}:{self.column}"

    def __repr__(self) -> str:
        return f"CodePosition({self.row}, {self.column})"


class PositionElement(Protocol):
    """Protocol for elements that have position information"""
    start: CodePosition
    end: CodePosition