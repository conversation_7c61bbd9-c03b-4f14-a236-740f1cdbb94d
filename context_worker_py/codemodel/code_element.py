"""
Code element definitions and data structures
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Protocol
from enum import Enum

from .position_element import CodePosition, PositionElement
from ..base.common.languages.languages import LanguageIdentifier


class StructureType(Enum):
    """Types of code structures"""
    CLASS = "class"
    INTERFACE = "interface"
    ENUM = "enum"
    STRUCT = "struct"
    ANNOTATION = "annotation"
    TRAIT = "trait"


class CodeElement(Protocol):
    """Base protocol for all code elements"""
    name: str


@dataclass
class CodeAnnotation:
    """Represents a code annotation"""
    name: str
    key_values: List[Dict[str, str]] = field(default_factory=list)


@dataclass
class CodeParameter:
    """Represents a function/method parameter"""
    name: str
    type: str


@dataclass
class CodeVariable(CodeElement, PositionElement):
    """Represents a variable or field"""
    name: str
    type: str
    start: CodePosition
    end: CodePosition
    is_system_type: bool = False


@dataclass
class CodeFunction(CodeElement, PositionElement):
    """Represents a function or method"""
    name: str
    vars: List[CodeVariable]  # Parameters as variables
    start: CodePosition
    end: CodePosition
    return_type: Optional[str] = None
    parameters: Optional[List[CodeParameter]] = None
    modifiers: Optional[str] = None
    annotations: Optional[List[CodeAnnotation]] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = []
        if self.annotations is None:
            self.annotations = []


@dataclass
class CodeStructure(CodeElement, PositionElement):
    """Represents a class, interface, or other structure"""
    name: str
    canonical_name: str  # Full qualified name like com.example.ExampleClass
    type: StructureType
    package: str
    start: CodePosition
    end: CodePosition
    extends: Optional[List[str]] = None
    implements: List[str] = field(default_factory=list)
    constant: Optional[List[CodeVariable]] = None
    fields: Optional[List[CodeVariable]] = None
    methods: List[CodeFunction] = field(default_factory=list)
    classes: Optional[List['CodeStructure']] = None  # Nested classes
    annotations: Optional[List[CodeAnnotation]] = None

    def __post_init__(self):
        if self.extends is None:
            self.extends = []
        if self.constant is None:
            self.constant = []
        if self.fields is None:
            self.fields = []
        if self.classes is None:
            self.classes = []
        if self.annotations is None:
            self.annotations = []


@dataclass
class CodeFile(CodeElement):
    """Represents a source code file"""
    name: str
    filepath: str
    language: LanguageIdentifier
    package: str
    imports: List[str]
    classes: List[CodeStructure]
    path: Optional[str] = None
    functions: Optional[List[CodeFunction]] = None

    def __post_init__(self):
        if self.functions is None:
            self.functions = []


def function_to_range(element: CodeFunction) -> 'TextRange':
    """Convert a CodeFunction to a TextRange"""
    from ..code_search.scope_graph.model.text_range import Point, TextRange

    start_point = Point(
        line=element.start.row,
        column=element.start.column,
        byte=0
    )
    end_point = Point(
        line=element.end.row,
        column=element.end.column,
        byte=0
    )
    return TextRange(start_point, end_point, '')