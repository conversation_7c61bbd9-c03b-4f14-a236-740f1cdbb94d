"""
Provider type definitions and interfaces

This module defines the core provider interfaces that correspond to the
TypeScript inversify service identifiers in the original codebase.
"""

from abc import ABC, abstractmethod
from typing import Any, List, Optional, Dict, Callable
from typing_extensions import Protocol


class LanguageProfile(Protocol):
    """
    Language profile interface for defining language-specific configurations
    """
    language_ids: List[str]
    file_extensions: List[str]

    def get_grammar(self, lang_service: Any, lang_id: Optional[str] = None) -> Any:
        """Get the tree-sitter grammar for this language"""
        ...


class RelevantCodeProvider(ABC):
    """
    Abstract base class for relevant code providers
    """

    @abstractmethod
    def get_relevant_code(self, context: Dict[str, Any]) -> List[Any]:
        """Get relevant code based on context"""
        pass


class StructurerProvider(ABC):
    """
    Code structure analysis provider

    Parse source code to structure data, see parseFile method.
    Structure data for CodeElement which can be CodeFile, CodeFunction, CodeVariable
    """

    @abstractmethod
    def parse_file(self, file_path: str, content: str) -> Any:
        """Parse file and return structured code elements"""
        pass

    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions"""
        pass


class HttpApiAnalyser(ABC):
    """
    REST API analysis provider

    Extract API resources and demands from code structure
    """

    @abstractmethod
    def source_code_analysis(self, code_elements: List[Any]) -> List[Any]:
        """Analyze source code and extract API information"""
        pass

    @abstractmethod
    def get_api_resources(self) -> List[Any]:
        """Get extracted API resources"""
        pass


# Service identifier constants (equivalent to TypeScript Symbol identifiers)
class ServiceIdentifiers:
    """Service identifier constants for dependency injection"""

    LANGUAGE_PROFILE = "LanguageProfile"
    RELEVANT_CODE_PROVIDER = "RelevantCodeProvider"
    STRUCTURER_PROVIDER = "IStructurerProvider"
    HTTP_API_ANALYSER = "IHttpApiAnalyser"