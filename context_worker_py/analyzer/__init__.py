"""
Code analysis components
"""

from .code_analysis_result import (
    Position,
    InterfaceImplementation,
    MultiImplementation,
    ClassExtension,
    MultiExtension,
    InheritanceHierarchy,
    CodeBlock,
    MarkdownAnalysisResult,
    SymbolInfo,
    FileSymbols,
    SymbolAnalysisResult,
    InterfaceAnalysisStats,
    ExtensionAnalysisStats,
    InterfaceAnalysisResult,
    ExtensionAnalysisResult,
    CodeAnalysisResult
)

from .file_system_scanner import FileSystemScanner
from .code_collector import CodeCollector

from .analyzers import (
    ICodeAnalyzer,
    InterfaceAnalyzer,
    SymbolAnalyser
)

__all__ = [
    # Analysis results
    "Position",
    "InterfaceImplementation",
    "MultiImplementation",
    "ClassExtension",
    "MultiExtension",
    "InheritanceHierarchy",
    "CodeBlock",
    "MarkdownAnalysisResult",
    "SymbolInfo",
    "FileSymbols",
    "SymbolAnalysisResult",
    "InterfaceAnalysisStats",
    "ExtensionAnalysisStats",
    "InterfaceAnalysisResult",
    "ExtensionAnalysisResult",
    "CodeAnalysisResult",

    # Core components
    "FileSystemScanner",
    "CodeCollector",

    # Analyzers
    "ICodeAnalyzer",
    "InterfaceAnalyzer",
    "SymbolAnalyser",
]