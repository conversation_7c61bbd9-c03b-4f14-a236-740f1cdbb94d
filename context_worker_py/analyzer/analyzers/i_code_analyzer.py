"""
Interface for code analyzers
"""

from abc import ABC, abstractmethod
from typing import Any, TYPE_CHECKING

if TYPE_CHECKING:
    from ..code_collector import CodeCollector


class ICodeAnalyzer(ABC):
    """Interface for code analyzers"""

    @abstractmethod
    async def analyze(self, code_collector: 'CodeCollector') -> Any:
        """
        Analyze code using the provided code collector

        Args:
            code_collector: CodeCollector instance with collected code structures

        Returns:
            Analysis result (type depends on specific analyzer)
        """
        pass