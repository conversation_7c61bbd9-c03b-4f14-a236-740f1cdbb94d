"""
Symbol analyzer for extracting and analyzing code symbols
"""

from typing import Dict, List, Any
from .i_code_analyzer import ICodeAnalyzer
from ..code_analysis_result import SymbolInfo, FileSymbols, SymbolAnalysisResult, Position
from ..code_collector import CodeCollector
from ...codemodel.position_element import CodePosition


class SymbolAnalyser(ICodeAnalyzer):
    """Analyzer for code symbols (classes, methods, functions, etc.)"""

    async def analyze(self, code_collector: CodeCollector) -> SymbolAnalysisResult:
        """
        Analyze symbols in the codebase

        Args:
            code_collector: CodeCollector with collected code structures

        Returns:
            SymbolAnalysisResult with symbol analysis data
        """
        all_symbols: List[SymbolInfo] = []
        file_symbols: Dict[str, FileSymbols] = {}

        # Process all code files
        for code_file in code_collector.get_all_code_structure():
            file_path = code_file.filepath
            symbols_in_file: List[SymbolInfo] = []

            class_count = 0
            method_count = 0

            # Extract symbols from classes
            for cls in code_file.classes:
                class_count += 1

                # Add class symbol
                class_symbol = SymbolInfo(
                    name=cls.name,
                    qualified_name=cls.canonical_name,
                    kind=1,  # Class kind
                    file_path=file_path,
                    comment="",  # TODO: Extract comments
                    position=Position(
                        start=CodePosition(cls.start.row, cls.start.column),
                        end=CodePosition(cls.end.row, cls.end.column)
                    )
                )
                symbols_in_file.append(class_symbol)
                all_symbols.append(class_symbol)

                # Extract method symbols
                for method in cls.methods:
                    method_count += 1

                    method_symbol = SymbolInfo(
                        name=method.name,
                        qualified_name=f"{cls.canonical_name}.{method.name}",
                        kind=2,  # Method kind
                        file_path=file_path,
                        comment="",  # TODO: Extract comments
                        position=Position(
                            start=CodePosition(method.start.row, method.start.column),
                            end=CodePosition(method.end.row, method.end.column)
                        )
                    )
                    symbols_in_file.append(method_symbol)
                    all_symbols.append(method_symbol)

            # Extract symbols from standalone functions
            if code_file.functions:
                for func in code_file.functions:
                    method_count += 1

                    func_symbol = SymbolInfo(
                        name=func.name,
                        qualified_name=f"{code_file.package}.{func.name}" if code_file.package else func.name,
                        kind=3,  # Function kind
                        file_path=file_path,
                        comment="",  # TODO: Extract comments
                        position=Position(
                            start=CodePosition(func.start.row, func.start.column),
                            end=CodePosition(func.end.row, func.end.column)
                        )
                    )
                    symbols_in_file.append(func_symbol)
                    all_symbols.append(func_symbol)

            # Create file symbols entry
            file_symbols[file_path] = FileSymbols(
                file_path=file_path,
                symbols=symbols_in_file,
                stats={
                    'classCount': class_count,
                    'methodCount': method_count,
                    'totalSymbols': len(symbols_in_file)
                }
            )

        # Calculate overall statistics
        total_symbols = len(all_symbols)

        # Group by file for statistics
        classes_by_file = []
        methods_by_file = []
        for file_path, file_symbol_info in file_symbols.items():
            classes_by_file.append({
                'filePath': file_path,
                'count': file_symbol_info.stats['classCount']
            })
            methods_by_file.append({
                'filePath': file_path,
                'count': file_symbol_info.stats['methodCount']
            })

        # Group by symbol kind
        symbols_by_kind: Dict[int, int] = {}
        for symbol in all_symbols:
            kind = symbol.kind
            symbols_by_kind[kind] = symbols_by_kind.get(kind, 0) + 1

        symbols_by_kind_list = [
            {'kind': kind, 'count': count}
            for kind, count in symbols_by_kind.items()
        ]

        stats = {
            'totalSymbols': total_symbols,
            'classesByFile': classes_by_file,
            'methodsByFile': methods_by_file,
            'symbolsByKind': symbols_by_kind_list
        }

        return SymbolAnalysisResult(
            symbols=all_symbols,
            file_symbols=file_symbols,
            stats=stats
        )