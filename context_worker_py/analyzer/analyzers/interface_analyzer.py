"""
Interface analyzer for analyzing interface implementations
"""

from typing import Dict, List, Any
from .i_code_analyzer import ICodeAnalyzer
from ..code_analysis_result import (
    InterfaceImplementation,
    MultiImplementation,
    InterfaceAnalysisStats,
    InterfaceAnalysisResult,
    Position
)
from ..code_collector import CodeCollector
from ...codemodel.position_element import CodePosition


class InterfaceAnalyzer(ICodeAnalyzer):
    """Analyzer for interface implementations"""

    async def analyze(self, code_collector: CodeCollector) -> InterfaceAnalysisResult:
        """
        Analyze interface implementations

        Args:
            code_collector: CodeCollector with collected code structures

        Returns:
            InterfaceAnalysisResult with interface analysis data
        """
        interface_map = code_collector.get_interface_map()
        implementation_map = code_collector.get_implementation_map()
        class_map = code_collector.get_class_map()

        return self.get_implementation_results(interface_map, implementation_map, class_map)

    def get_implementation_results(
        self,
        interface_map: Dict[str, Dict[str, Any]],
        implementation_map: Dict[str, List[Any]],
        class_map: Dict[str, Dict[str, Any]]
    ) -> InterfaceAnalysisResult:
        """Get interface implementation analysis results"""

        interfaces: List[InterfaceImplementation] = []
        multi_implementers: List[MultiImplementation] = []

        # Analyze interfaces and their implementations
        for interface_key, interface_info in interface_map.items():
            interface_obj = interface_info['interface']
            implementations = implementation_map.get(interface_key, [])

            if implementations:
                # Create interface implementation result
                interface_impl = InterfaceImplementation(
                    interface_name=interface_obj.name,
                    interface_file=interface_info['file'],
                    method_count=len(interface_obj.methods) if interface_obj.methods else 0,
                    package=interface_obj.package,
                    position=Position(
                        start=CodePosition(interface_obj.start.row, interface_obj.start.column),
                        end=CodePosition(interface_obj.end.row, interface_obj.end.column)
                    ),
                    implementations=implementations
                )
                interfaces.append(interface_impl)

        # Find classes that implement multiple interfaces
        class_interface_count: Dict[str, List[str]] = {}

        for interface_key, implementations in implementation_map.items():
            for impl in implementations:
                class_name = impl['className']
                if class_name not in class_interface_count:
                    class_interface_count[class_name] = []
                class_interface_count[class_name].append(interface_key)

        # Create multi-implementation results
        for class_name, interface_keys in class_interface_count.items():
            if len(interface_keys) > 1:
                # Find class info
                class_info = None
                for key, info in class_map.items():
                    if info['class'].name == class_name:
                        class_info = info
                        break

                if class_info:
                    cls = class_info['class']

                    # Get interface details
                    interface_details = []
                    for interface_key in interface_keys:
                        if interface_key in interface_map:
                            intf = interface_map[interface_key]
                            interface_details.append({
                                'interfaceName': intf['interface'].name,
                                'interfaceFile': intf['file'],
                                'position': {
                                    'start': {
                                        'row': intf['interface'].start.row,
                                        'column': intf['interface'].start.column
                                    },
                                    'end': {
                                        'row': intf['interface'].end.row,
                                        'column': intf['interface'].end.column
                                    }
                                }
                            })

                    multi_impl = MultiImplementation(
                        class_name=class_name,
                        class_file=class_info['file'],
                        interface_count=len(interface_keys),
                        position=Position(
                            start=CodePosition(cls.start.row, cls.start.column),
                            end=CodePosition(cls.end.row, cls.end.column)
                        ),
                        interfaces=interface_details
                    )
                    multi_implementers.append(multi_impl)

        # Calculate statistics
        total_interfaces = len(interface_map)
        implemented_interfaces = len([intf for intf in interfaces if intf.implementations])
        unimplemented_interfaces = total_interfaces - implemented_interfaces
        multi_implementer_count = len(multi_implementers)

        stats = InterfaceAnalysisStats(
            total_interfaces=total_interfaces,
            implemented_interfaces=implemented_interfaces,
            unimplemented_interfaces=unimplemented_interfaces,
            multi_implementer_count=multi_implementer_count
        )

        return InterfaceAnalysisResult(
            interfaces=interfaces,
            multi_implementers=multi_implementers,
            stats=stats
        )