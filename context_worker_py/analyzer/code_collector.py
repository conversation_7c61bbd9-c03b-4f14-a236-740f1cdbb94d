"""
Code collector for gathering and organizing code structures
"""

from typing import Dict, List, Optional, Any
from ..base.common.languages.languages import infer_language
from ..codemodel.code_element import CodeFile, CodeStructure, StructureType


class CodeCollector:
    """Collects and organizes code structures for analysis"""

    def __init__(self, workspace_path: str):
        self.workspace_path = workspace_path
        self.interface_map: Dict[str, Dict[str, Any]] = {}
        self.class_map: Dict[str, Dict[str, Any]] = {}
        self.implementation_map: Dict[str, List[Any]] = {}
        self.extension_map: Dict[str, List[Any]] = {}
        self.all_files: List[str] = []
        self.all_code_files: List[CodeFile] = []

    def infer_language(self, file_path: str) -> Optional[str]:
        """Infer language from file path"""
        return infer_language(file_path)

    def add_code_file(self, file_path: str, code_file: CodeFile) -> None:
        """Add a code file to the collector"""
        if not code_file.classes:
            return

        self.all_code_files.append(code_file)

        # Process interfaces
        file_interfaces = [cls for cls in code_file.classes if cls.type == StructureType.INTERFACE]
        for intf in file_interfaces:
            key = intf.canonical_name or f"{intf.package}.{intf.name}"

            # Keep the interface with more methods if duplicate
            if (key not in self.interface_map or
                (intf.methods and self.interface_map[key]['interface'].methods and
                 len(intf.methods) > len(self.interface_map[key]['interface'].methods))):
                self.interface_map[key] = {
                    'file': file_path,
                    'interface': intf
                }

        # Process classes
        file_classes = [cls for cls in code_file.classes if cls.type == StructureType.CLASS]
        for cls in file_classes:
            key = cls.canonical_name or f"{cls.package}.{cls.name}"
            self.class_map[key] = {
                'file': file_path,
                'class': cls
            }

        self.process_class_relationships()

    def process_class_relationships(self) -> None:
        """Process class relationships (implementations and extensions)"""
        self.implementation_map.clear()
        self.extension_map.clear()

        for class_key, class_info in self.class_map.items():
            cls = class_info['class']

            # Process interface implementations
            for interface_name in cls.implements:
                interface_key = self.find_interface_key(interface_name, cls.package)
                if interface_key:
                    if interface_key not in self.implementation_map:
                        self.implementation_map[interface_key] = []

                    self.implementation_map[interface_key].append({
                        'className': cls.name,
                        'classFile': class_info['file'],
                        'position': {
                            'start': {'row': cls.start.row, 'column': cls.start.column},
                            'end': {'row': cls.end.row, 'column': cls.end.column}
                        }
                    })

            # Process class extensions
            if cls.extends:
                for parent_name in cls.extends:
                    parent_key = self.find_class_key(parent_name, cls.package)
                    if parent_key:
                        if parent_key not in self.extension_map:
                            self.extension_map[parent_key] = []

                        self.extension_map[parent_key].append({
                            'className': cls.name,
                            'classFile': class_info['file'],
                            'position': {
                                'start': {'row': cls.start.row, 'column': cls.start.column},
                                'end': {'row': cls.end.row, 'column': cls.end.column}
                            }
                        })

    def find_interface_key(self, interface_name: str, class_package: str) -> Optional[str]:
        """Find interface key by name and package"""
        # Direct match
        if interface_name in self.interface_map:
            return interface_name

        # Try with package
        packaged_name = f"{class_package}.{interface_name}"
        if packaged_name in self.interface_map:
            return packaged_name

        # Search by partial match
        for key, value in self.interface_map.items():
            if (key.endswith(f".{interface_name}") or
                value['interface'].name == interface_name):
                return key

        return None

    def find_class_key(self, parent_class_name: str, class_package: str) -> Optional[str]:
        """Find class key by name and package"""
        # Direct match
        if parent_class_name in self.class_map:
            return parent_class_name

        # Try with package
        packaged_name = f"{class_package}.{parent_class_name}"
        if packaged_name in self.class_map:
            return packaged_name

        # Search by partial match
        for key, value in self.class_map.items():
            if (key.endswith(f".{parent_class_name}") or
                value['class'].name == parent_class_name):
                return key

        return None

    # Getter methods
    def get_interface_map(self) -> Dict[str, Dict[str, Any]]:
        """Get interface map"""
        return self.interface_map

    def get_class_map(self) -> Dict[str, Dict[str, Any]]:
        """Get class map"""
        return self.class_map

    def get_implementation_map(self) -> Dict[str, List[Any]]:
        """Get implementation map"""
        return self.implementation_map

    def get_extension_map(self) -> Dict[str, List[Any]]:
        """Get extension map"""
        return self.extension_map

    def set_all_files(self, files: List[Dict[str, str]]) -> None:
        """Set all files from file info list"""
        self.all_files = [file_info['file'] for file_info in files]

    def get_all_files(self) -> List[str]:
        """Get all file paths"""
        return self.all_files

    def get_workspace_path(self) -> str:
        """Get workspace path"""
        return self.workspace_path

    def get_all_code_structure(self) -> List[CodeFile]:
        """Get all code files"""
        return self.all_code_files

    def clear(self) -> None:
        """Clear all collected data"""
        self.interface_map.clear()
        self.class_map.clear()
        self.implementation_map.clear()
        self.extension_map.clear()
        self.all_files.clear()
        self.all_code_files.clear()

    def get_stats(self) -> Dict[str, int]:
        """Get collection statistics"""
        return {
            'total_files': len(self.all_files),
            'total_code_files': len(self.all_code_files),
            'total_interfaces': len(self.interface_map),
            'total_classes': len(self.class_map),
            'total_implementations': sum(len(impls) for impls in self.implementation_map.values()),
            'total_extensions': sum(len(exts) for exts in self.extension_map.values())
        }