"""
Code analysis result data structures
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from ..codemodel.position_element import CodePosition


@dataclass
class Position:
    """Position information for code elements"""
    start: CodePosition
    end: CodePosition


@dataclass
class InterfaceImplementation:
    """Interface implementation analysis result"""
    interface_name: str
    interface_file: str
    method_count: int
    package: str
    position: Position
    implementations: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class MultiImplementation:
    """Multiple interface implementation analysis result"""
    class_name: str
    class_file: str
    interface_count: int
    position: Optional[Position] = None
    interfaces: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class ClassExtension:
    """Class extension analysis result"""
    parent_name: str
    parent_file: str
    package: str
    position: Position
    children: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class MultiExtension:
    """Multiple extension analysis result"""
    class_name: str
    class_file: str
    parent_count: int
    position: Optional[Position] = None
    parents: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class InheritanceHierarchy:
    """Inheritance hierarchy analysis result"""
    max_depth: int
    deepest_classes: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class CodeBlock:
    """Code block from markdown analysis"""
    file_path: str
    title: str
    heading: str
    language: str
    internal_language: str
    code: str
    position: Optional[Position] = None


@dataclass
class MarkdownAnalysisResult:
    """Markdown analysis result"""
    code_blocks: List[CodeBlock]
    total_count: int


@dataclass
class SymbolInfo:
    """Symbol information"""
    name: str
    qualified_name: str
    kind: int
    file_path: str
    comment: str
    position: Position


@dataclass
class FileSymbols:
    """File symbols information"""
    file_path: str
    symbols: List[SymbolInfo]
    stats: Dict[str, int]  # classCount, methodCount, totalSymbols


@dataclass
class SymbolAnalysisResult:
    """Symbol analysis result"""
    symbols: List[SymbolInfo]
    file_symbols: Dict[str, FileSymbols]  # filepath -> FileSymbols
    stats: Dict[str, Any]


@dataclass
class InterfaceAnalysisStats:
    """Interface analysis statistics"""
    total_interfaces: int
    implemented_interfaces: int
    unimplemented_interfaces: int
    multi_implementer_count: int


@dataclass
class ExtensionAnalysisStats:
    """Extension analysis statistics"""
    extended_class_count: int
    total_extension_relations: int
    multi_extended_class_count: int


@dataclass
class InterfaceAnalysisResult:
    """Interface analysis result"""
    interfaces: List[InterfaceImplementation]
    multi_implementers: List[MultiImplementation]
    stats: InterfaceAnalysisStats


@dataclass
class ExtensionAnalysisResult:
    """Extension analysis result"""
    extensions: List[ClassExtension]
    multi_extensions: List[MultiExtension]
    hierarchy: InheritanceHierarchy
    stats: ExtensionAnalysisStats


@dataclass
class CodeAnalysisResult:
    """Complete code analysis result"""
    interface_analysis: InterfaceAnalysisResult
    extension_analysis: ExtensionAnalysisResult
    markdown_analysis: Optional[MarkdownAnalysisResult] = None
    symbol_analysis: Optional[SymbolAnalysisResult] = None