"""
File system scanner for code analysis
"""

import os
import asyncio
from pathlib import Path
from typing import List, Set, Dict
import fnmatch


# Default patterns to ignore
DEFAULT_IGNORE_PATTERNS = [
    # Dependencies
    'node_modules',
    'vendor',
    'bower_components',
    'jspm_packages',

    # Build outputs
    'dist',
    'build',
    'out',
    '.next',
    'target',
    'coverage',

    # Gradle
    '.gradle',
    'gradle',
    'gradlew',
    'gradlew.bat',
    'gradle.properties',
    'gradle-wrapper.properties',
    'gradle-wrapper.jar',

    # Cache directories
    '.cache',
    '.parcel-cache',
    '.turbo',
    '.yarn',

    # IDE and editor files
    '.idea',
    '.vscode',
    '*.swp',
    '*.swo',
    '.DS_Store',

    # Logs
    '*.log',
    'logs',

    # Environment files
    '.env*',
    '.env.local',
    '.env.*.local',

    # Package manager files
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',

    # Test coverage
    '.nyc_output',

    # Temporary files
    'tmp',
    'temp',
    '*.tmp',

    # System files
    'Thumbs.db'
]


class FileSystemScanner:
    """Handles file system operations for scanning directories and files"""

    def __init__(self):
        self.ignore_patterns: List[str] = DEFAULT_IGNORE_PATTERNS.copy()
        self.gitignore_cache: Dict[str, List[str]] = {}

    async def scan_directory(self, dir_path: str, base_dir: str = None) -> List[str]:
        """
        Scan a directory and return all file paths, respecting ignore patterns and .gitignore files

        Args:
            dir_path: Directory to scan
            base_dir: Base directory for relative path calculation

        Returns:
            List of file paths
        """
        if base_dir is None:
            base_dir = dir_path

        relative_path = os.path.relpath(dir_path, base_dir)
        if self.should_ignore_path(relative_path):
            return []

        await self.parse_gitignore_if_exists(dir_path, base_dir)

        try:
            entries = os.listdir(dir_path)
        except (OSError, PermissionError):
            return []

        files = []
        for entry in entries:
            full_path = os.path.join(dir_path, entry)
            rel_path = os.path.relpath(full_path, base_dir)

            if self.should_ignore_path(rel_path) or self.is_ignored_by_gitignore(rel_path, base_dir):
                continue

            if os.path.isdir(full_path):
                # Recursively scan subdirectory
                subdir_files = await self.scan_directory(full_path, base_dir)
                files.extend(subdir_files)
            else:
                try:
                    # Check file size (skip files larger than 1MB)
                    stat_info = os.stat(full_path)
                    if stat_info.st_size > 1024 * 1024:
                        continue

                    files.append(full_path)
                except (OSError, PermissionError):
                    continue

        return files

    def should_ignore_path(self, path: str) -> bool:
        """Check if a path should be ignored based on patterns"""
        path_parts = Path(path).parts

        for pattern in self.ignore_patterns:
            # Check if any part of the path matches the pattern
            for part in path_parts:
                if fnmatch.fnmatch(part, pattern):
                    return True

            # Check the full path against the pattern
            if fnmatch.fnmatch(path, pattern):
                return True

        return False

    async def parse_gitignore_if_exists(self, dir_path: str, base_dir: str) -> None:
        """Parse .gitignore file if it exists in the directory"""
        gitignore_path = os.path.join(dir_path, '.gitignore')

        if not os.path.isfile(gitignore_path):
            return

        if gitignore_path in self.gitignore_cache:
            return

        try:
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            patterns = []
            for line in lines:
                line = line.strip()
                # Skip empty lines and comments
                if not line or line.startswith('#'):
                    continue
                patterns.append(line)

            self.gitignore_cache[gitignore_path] = patterns
        except (OSError, UnicodeDecodeError):
            self.gitignore_cache[gitignore_path] = []

    def is_ignored_by_gitignore(self, rel_path: str, base_dir: str) -> bool:
        """Check if a path is ignored by any .gitignore file"""
        current_dir = base_dir
        path_to_check = rel_path

        while True:
            gitignore_path = os.path.join(current_dir, '.gitignore')

            if gitignore_path in self.gitignore_cache:
                patterns = self.gitignore_cache[gitignore_path]

                for pattern in patterns:
                    # Handle negation patterns
                    if pattern.startswith('!'):
                        continue  # Skip negation for simplicity

                    # Handle directory patterns
                    if pattern.endswith('/'):
                        pattern = pattern[:-1]
                        if fnmatch.fnmatch(path_to_check, pattern + '*'):
                            return True
                    else:
                        if fnmatch.fnmatch(path_to_check, pattern):
                            return True

                        # Check if any parent directory matches
                        path_parts = Path(path_to_check).parts
                        for i in range(len(path_parts)):
                            partial_path = '/'.join(path_parts[:i+1])
                            if fnmatch.fnmatch(partial_path, pattern):
                                return True

            # Move up to parent directory
            parent_dir = os.path.dirname(current_dir)
            if parent_dir == current_dir:
                break
            current_dir = parent_dir

            # Update path relative to new current directory
            try:
                path_to_check = os.path.relpath(os.path.join(base_dir, rel_path), current_dir)
            except ValueError:
                break

        return False

    async def read_file_content(self, file_path: str) -> str:
        """Read file content asynchronously"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._read_file_sync, file_path)

    def _read_file_sync(self, file_path: str) -> str:
        """Synchronous file reading"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()

    def add_ignore_pattern(self, pattern: str) -> None:
        """Add a custom ignore pattern"""
        if pattern not in self.ignore_patterns:
            self.ignore_patterns.append(pattern)

    def remove_ignore_pattern(self, pattern: str) -> None:
        """Remove an ignore pattern"""
        if pattern in self.ignore_patterns:
            self.ignore_patterns.remove(pattern)

    def get_ignore_patterns(self) -> List[str]:
        """Get current ignore patterns"""
        return self.ignore_patterns.copy()