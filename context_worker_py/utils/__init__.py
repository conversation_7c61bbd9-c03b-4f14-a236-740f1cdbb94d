"""
Utility functions and helpers
"""

from .path_utils import (
    to_posix_path,
    normalize_path,
    are_paths_equal,
    get_readable_path,
    to_relative_path,
    ensure_directory_exists,
    get_file_extension,
    get_filename_without_extension,
    join_paths,
    is_absolute_path
)

from .file_utils import (
    is_larger_than_500kb,
    file_exists,
    file_exists_sync,
    ensure_dir,
    safe_read_file,
    safe_read_file_sync,
    safe_write_file,
    get_file_size,
    get_file_lines,
    read_file_range,
    count_lines_words_chars,
    get_file_stats
)

from .string_utils import (
    capitalize,
    truncate_string,
    strip_ansi_colors,
    parse_version,
    generate_id,
    create_progress_bar,
    camel_to_snake,
    snake_to_camel,
    pascal_case,
    kebab_case,
    normalize_whitespace,
    extract_words,
    similarity_ratio,
    debounce,
    is_development,
    is_production
)

from .scm_loader import (
    load_scm_file,
    find_scm_files,
    validate_scm_content
)

__all__ = [
    # Path utilities
    "to_posix_path",
    "normalize_path",
    "are_paths_equal",
    "get_readable_path",
    "to_relative_path",
    "ensure_directory_exists",
    "get_file_extension",
    "get_filename_without_extension",
    "join_paths",
    "is_absolute_path",

    # File utilities
    "is_larger_than_500kb",
    "file_exists",
    "file_exists_sync",
    "ensure_dir",
    "safe_read_file",
    "safe_read_file_sync",
    "safe_write_file",
    "get_file_size",
    "get_file_lines",
    "read_file_range",
    "count_lines_words_chars",
    "get_file_stats",

    # String utilities
    "capitalize",
    "truncate_string",
    "strip_ansi_colors",
    "parse_version",
    "generate_id",
    "create_progress_bar",
    "camel_to_snake",
    "snake_to_camel",
    "pascal_case",
    "kebab_case",
    "normalize_whitespace",
    "extract_words",
    "similarity_ratio",
    "debounce",
    "is_development",
    "is_production",

    # SCM utilities
    "load_scm_file",
    "find_scm_files",
    "validate_scm_content",
]