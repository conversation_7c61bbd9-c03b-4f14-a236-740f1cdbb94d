"""
Path utilities for cross-platform path handling
"""

import os
import platform
from pathlib import Path
from typing import Optional


def to_posix_path(path: str) -> str:
    """
    Convert a path to POSIX format (forward slashes)

    This ensures consistent path presentation across platforms while
    maintaining compatibility with the underlying file system.

    Args:
        path: The path to convert

    Returns:
        Path with forward slashes
    """
    if not path:
        return path

    # Convert to Path object and back to string with forward slashes
    return str(Path(path).as_posix())


def normalize_path(path: str) -> str:
    """
    Normalize a path for the current platform

    Args:
        path: The path to normalize

    Returns:
        Normalized path
    """
    if not path:
        return path

    return os.path.normpath(path)


def are_paths_equal(path1: Optional[str], path2: Optional[str]) -> bool:
    """
    Safe path comparison that works across different platforms

    Args:
        path1: First path to compare
        path2: Second path to compare

    Returns:
        True if paths are equal, False otherwise
    """
    if not path1 and not path2:
        return True
    if not path1 or not path2:
        return False

    path1 = normalize_path(path1)
    path2 = normalize_path(path2)

    # Case-insensitive comparison on Windows
    if platform.system().lower() == "windows":
        return path1.lower() == path2.lower()

    return path1 == path2


def get_readable_path(cwd: str, rel_path: Optional[str] = None) -> str:
    """
    Get a human-readable path relative to the current working directory

    Args:
        cwd: Current working directory
        rel_path: Relative path (optional)

    Returns:
        Human-readable path
    """
    rel_path = rel_path or ""

    # Resolve the absolute path
    absolute_path = os.path.abspath(os.path.join(cwd, rel_path))

    # Check if we're at the Desktop (user opened without workspace)
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    if are_paths_equal(cwd, desktop_path):
        return to_posix_path(absolute_path)

    # If it's the same as cwd, just return the basename
    if are_paths_equal(absolute_path, cwd):
        return to_posix_path(os.path.basename(absolute_path))

    # Show relative path to cwd
    try:
        relative_path = os.path.relpath(absolute_path, cwd)
        if absolute_path.startswith(cwd):
            return to_posix_path(relative_path)
        else:
            # Outside cwd, show absolute path
            return to_posix_path(absolute_path)
    except ValueError:
        # Different drives on Windows, return absolute path
        return to_posix_path(absolute_path)


def to_relative_path(file_path: str, cwd: str) -> str:
    """
    Convert file path to relative path from cwd

    Args:
        file_path: The file path to convert
        cwd: Current working directory

    Returns:
        Relative path with POSIX separators
    """
    try:
        relative_path = os.path.relpath(file_path, cwd)
        relative_path = to_posix_path(relative_path)

        # Preserve trailing slash if original path had one
        if file_path.endswith(("/", "\\")):
            relative_path += "/"

        return relative_path
    except ValueError:
        # Different drives on Windows, return original path
        return to_posix_path(file_path)


def ensure_directory_exists(dir_path: str) -> None:
    """
    Ensure that a directory exists, creating it if necessary

    Args:
        dir_path: Directory path to ensure exists
    """
    Path(dir_path).mkdir(parents=True, exist_ok=True)


def get_file_extension(file_path: str) -> str:
    """
    Get the file extension from a file path

    Args:
        file_path: The file path

    Returns:
        File extension (without the dot)
    """
    return Path(file_path).suffix.lstrip('.')


def get_filename_without_extension(file_path: str) -> str:
    """
    Get filename without extension

    Args:
        file_path: The file path

    Returns:
        Filename without extension
    """
    return Path(file_path).stem


def join_paths(*paths: str) -> str:
    """
    Join multiple paths using the appropriate separator

    Args:
        *paths: Path components to join

    Returns:
        Joined path
    """
    return str(Path(*paths))


def is_absolute_path(path: str) -> bool:
    """
    Check if a path is absolute

    Args:
        path: Path to check

    Returns:
        True if path is absolute, False otherwise
    """
    return Path(path).is_absolute()