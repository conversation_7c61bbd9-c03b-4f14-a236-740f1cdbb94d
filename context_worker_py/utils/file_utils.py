"""
File utilities for file operations and content handling
"""

import os
import async<PERSON>
from pathlib import Path
from typing import Op<PERSON>, List, Tuple


def is_larger_than_500kb(content: str) -> bool:
    """
    Check if content is larger than 500KB

    Args:
        content: The content to check

    Returns:
        True if content is larger than 500KB, False otherwise
    """
    return len(content) > 500000


async def file_exists(file_path: str) -> bool:
    """
    Check if a file exists asynchronously

    Args:
        file_path: Path to the file

    Returns:
        True if file exists, False otherwise
    """
    try:
        return Path(file_path).exists()
    except (OSError, ValueError):
        return False


def file_exists_sync(file_path: str) -> bool:
    """
    Check if a file exists synchronously

    Args:
        file_path: Path to the file

    Returns:
        True if file exists, False otherwise
    """
    try:
        return Path(file_path).exists()
    except (OSError, ValueError):
        return False


async def ensure_dir(dir_path: str) -> None:
    """
    Ensure that a directory exists, creating it if necessary

    Args:
        dir_path: Directory path to ensure exists
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    except (OSError, ValueError) as e:
        print(f"Error creating directory {dir_path}: {e}")


async def safe_read_file(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
    """
    Safely read a file asynchronously

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        File content as string, or None if error occurred
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except (OSError, UnicodeDecodeError, ValueError):
        return None


def safe_read_file_sync(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
    """
    Safely read a file synchronously

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        File content as string, or None if error occurred
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except (OSError, UnicodeDecodeError, ValueError):
        return None


async def safe_write_file(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
    """
    Safely write content to a file asynchronously

    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding (default: utf-8)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Ensure parent directory exists
        parent_dir = Path(file_path).parent
        parent_dir.mkdir(parents=True, exist_ok=True)

        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        return True
    except (OSError, UnicodeEncodeError, ValueError):
        return False


def get_file_size(file_path: str) -> int:
    """
    Get file size in bytes

    Args:
        file_path: Path to the file

    Returns:
        File size in bytes, or 0 if error occurred
    """
    try:
        return Path(file_path).stat().st_size
    except (OSError, ValueError):
        return 0


def get_file_lines(file_path: str, encoding: str = 'utf-8') -> List[str]:
    """
    Get file content as list of lines

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        List of lines, or empty list if error occurred
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.readlines()
    except (OSError, UnicodeDecodeError, ValueError):
        return []


def read_file_range(file_path: str, start_line: int, end_line: int,
                   encoding: str = 'utf-8') -> Optional[str]:
    """
    Read a specific range of lines from a file

    Args:
        file_path: Path to the file
        start_line: Start line number (1-based)
        end_line: End line number (1-based, -1 for end of file)
        encoding: File encoding (default: utf-8)

    Returns:
        Content of the specified line range, or None if error occurred
    """
    try:
        lines = get_file_lines(file_path, encoding)
        if not lines:
            return None

        # Convert to 0-based indexing
        start_idx = max(0, start_line - 1)
        end_idx = len(lines) if end_line == -1 else min(len(lines), end_line)

        if start_idx >= len(lines):
            return ""

        selected_lines = lines[start_idx:end_idx]
        return ''.join(selected_lines)
    except (OSError, UnicodeDecodeError, ValueError):
        return None


def count_lines_words_chars(content: str) -> Tuple[int, int, int]:
    """
    Count lines, words, and characters in content

    Args:
        content: The content to analyze

    Returns:
        Tuple of (lines, words, characters)
    """
    if not content:
        return (0, 0, 0)

    lines = content.count('\n') + (1 if content and not content.endswith('\n') else 0)
    words = len([word for word in content.split() if word])
    chars = len(content)

    return (lines, words, chars)


def get_file_stats(file_path: str, encoding: str = 'utf-8') -> Optional[Tuple[int, int, int, int]]:
    """
    Get file statistics: lines, words, characters, and bytes

    Args:
        file_path: Path to the file
        encoding: File encoding (default: utf-8)

    Returns:
        Tuple of (lines, words, characters, bytes) or None if error occurred
    """
    try:
        content = safe_read_file_sync(file_path, encoding)
        if content is None:
            return None

        lines, words, chars = count_lines_words_chars(content)
        bytes_count = len(content.encode(encoding))

        return (lines, words, chars, bytes_count)
    except (UnicodeEncodeError, ValueError):
        return None