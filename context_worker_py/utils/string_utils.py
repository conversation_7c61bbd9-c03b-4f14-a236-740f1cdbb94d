"""
String utilities for text processing and manipulation
"""

import re
import time
from typing import Optional, Dict, Any, Callable, TypeVar, List


def capitalize(text: str) -> str:
    """
    Capitalize the first letter of a string

    Args:
        text: The string to capitalize

    Returns:
        String with first letter capitalized
    """
    if not text or not isinstance(text, str):
        return ""
    return text[0].upper() + text[1:] if len(text) > 1 else text.upper()


def truncate_string(text: str, max_length: int, suffix: str = '...') -> str:
    """
    Truncate a string to a maximum length

    Args:
        text: The string to truncate
        max_length: Maximum length of the result
        suffix: Suffix to add when truncating (default: '...')

    Returns:
        Truncated string
    """
    if not text or len(text) <= max_length:
        return text

    return text[:max_length - len(suffix)] + suffix


def strip_ansi_colors(text: str) -> str:
    """
    Remove ANSI color codes from a string

    Args:
        text: The string to clean

    Returns:
        String without ANSI color codes
    """
    ansi_escape = re.compile(r'\x1b\[[0-9;]*m')
    return ansi_escape.sub('', text)


def parse_version(version: str) -> Optional[Dict[str, int]]:
    """
    Parse a version string into major, minor, patch components

    Args:
        version: Version string (e.g., "1.2.3")

    Returns:
        Dictionary with major, minor, patch keys, or None if invalid
    """
    match = re.match(r'^(\d+)\.(\d+)\.(\d+)', version)
    if not match:
        return None

    return {
        'major': int(match.group(1)),
        'minor': int(match.group(2)),
        'patch': int(match.group(3))
    }


def generate_id(prefix: str = '') -> str:
    """
    Generate a unique ID

    Args:
        prefix: Optional prefix for the ID

    Returns:
        Unique ID string
    """
    import random

    timestamp = str(int(time.time() * 1000))  # milliseconds
    random_part = ''.join(random.choices('0123456789abcdef', k=8))

    if prefix:
        return f"{prefix}-{timestamp}-{random_part}"
    return f"{timestamp}-{random_part}"


def create_progress_bar(progress: float, width: int = 20) -> str:
    """
    Create a progress bar string

    Args:
        progress: Progress percentage (0-100)
        width: Width of the progress bar in characters

    Returns:
        Progress bar string
    """
    filled = int((progress / 100) * width)
    empty = width - filled

    return f"[{'█' * filled}{' ' * empty}] {progress:.1f}%"


def camel_to_snake(name: str) -> str:
    """
    Convert camelCase to snake_case

    Args:
        name: camelCase string

    Returns:
        snake_case string
    """
    # Insert underscore before uppercase letters
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()


def snake_to_camel(name: str) -> str:
    """
    Convert snake_case to camelCase

    Args:
        name: snake_case string

    Returns:
        camelCase string
    """
    components = name.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


def pascal_case(name: str) -> str:
    """
    Convert string to PascalCase

    Args:
        name: Input string

    Returns:
        PascalCase string
    """
    if '_' in name:
        # snake_case to PascalCase
        return ''.join(word.capitalize() for word in name.split('_'))
    else:
        # camelCase to PascalCase
        return name[0].upper() + name[1:] if name else ""


def kebab_case(name: str) -> str:
    """
    Convert string to kebab-case

    Args:
        name: Input string

    Returns:
        kebab-case string
    """
    if '_' in name:
        # snake_case to kebab-case
        return name.replace('_', '-')
    else:
        # camelCase to kebab-case
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1-\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1-\2', s1).lower()


def normalize_whitespace(text: str) -> str:
    """
    Normalize whitespace in a string (collapse multiple spaces to single space)

    Args:
        text: Input text

    Returns:
        Text with normalized whitespace
    """
    return re.sub(r'\s+', ' ', text.strip())


def extract_words(text: str) -> List[str]:
    """
    Extract words from text (alphanumeric sequences)

    Args:
        text: Input text

    Returns:
        List of words
    """
    return re.findall(r'\b\w+\b', text.lower())


def similarity_ratio(text1: str, text2: str) -> float:
    """
    Calculate similarity ratio between two strings using simple character comparison

    Args:
        text1: First string
        text2: Second string

    Returns:
        Similarity ratio (0.0 to 1.0)
    """
    if not text1 and not text2:
        return 1.0
    if not text1 or not text2:
        return 0.0

    # Simple character-based similarity
    set1 = set(text1.lower())
    set2 = set(text2.lower())

    intersection = len(set1 & set2)
    union = len(set1 | set2)

    return intersection / union if union > 0 else 0.0


# Type variable for debounce function
T = TypeVar('T', bound=Callable[..., Any])


def debounce(wait: float) -> Callable[[T], T]:
    """
    Debounce decorator to limit function calls

    Args:
        wait: Wait time in seconds

    Returns:
        Decorated function
    """
    def decorator(func: T) -> T:
        last_called = [0.0]

        def wrapper(*args, **kwargs):
            now = time.time()
            if now - last_called[0] >= wait:
                last_called[0] = now
                return func(*args, **kwargs)

        return wrapper  # type: ignore

    return decorator


def is_development() -> bool:
    """
    Check if running in development environment

    Returns:
        True if in development mode
    """
    import os
    return os.environ.get('NODE_ENV') == 'development'


def is_production() -> bool:
    """
    Check if running in production environment

    Returns:
        True if in production mode
    """
    import os
    return os.environ.get('NODE_ENV') == 'production'