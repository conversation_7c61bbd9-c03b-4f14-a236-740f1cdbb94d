"""
SCM (Source Code Management) file loader utility
"""

import os
from pathlib import Path
from typing import Optional


def load_scm_file(relative_path: str, base_dir: Optional[str] = None) -> str:
    """
    Load .scm file content

    Args:
        relative_path: Relative path to the .scm file
        base_dir: Base directory to resolve from (defaults to current file's directory)

    Returns:
        Content of the .scm file as string, empty string if error
    """
    try:
        if base_dir is None:
            # Use the directory of this module as base
            base_dir = Path(__file__).parent
        else:
            base_dir = Path(base_dir)

        full_path = base_dir / relative_path

        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    except (OSError, UnicodeDecodeError) as e:
        print(f"Error loading SCM file: {relative_path}, error: {e}")
        return ""


def find_scm_files(directory: str, recursive: bool = True) -> list[str]:
    """
    Find all .scm files in a directory

    Args:
        directory: Directory to search in
        recursive: Whether to search recursively

    Returns:
        List of .scm file paths
    """
    scm_files = []
    dir_path = Path(directory)

    if not dir_path.exists() or not dir_path.is_dir():
        return scm_files

    try:
        if recursive:
            pattern = "**/*.scm"
        else:
            pattern = "*.scm"

        for scm_file in dir_path.glob(pattern):
            if scm_file.is_file():
                scm_files.append(str(scm_file))
    except OSError:
        pass

    return scm_files


def validate_scm_content(content: str) -> bool:
    """
    Basic validation of SCM file content

    Args:
        content: SCM file content

    Returns:
        True if content appears to be valid SCM syntax
    """
    if not content.strip():
        return False

    # Basic SCM syntax check - should contain parentheses
    return '(' in content and ')' in content