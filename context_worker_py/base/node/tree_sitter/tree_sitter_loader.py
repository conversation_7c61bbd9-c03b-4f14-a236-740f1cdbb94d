"""
Tree-sitter language loader for Python
"""

import os
import asyncio
from typing import Dict, Optional, Callable, Awaitable
from dataclasses import dataclass

try:
    import tree_sitter_languages
    from tree_sitter import Language, Parser, Tree
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Parser = None
    Tree = None


def format_wasm_filename(template: str, language_id: str) -> str:
    """Format WASM filename template with language ID"""
    return template.replace('{language}', language_id)


@dataclass
class TreeSitterLoaderOptions:
    """Tree-sitter loader configuration options"""
    path_template: Optional[str] = None
    path_factory: Optional[Callable[[str], str]] = None
    read_file: Optional[Callable[[str], Awaitable[bytes]]] = None


class TreeSitterLoader:
    """Tree-sitter language loader and parser manager"""

    def __init__(self, options: TreeSitterLoaderOptions):
        if not HAS_TREE_SITTER:
            raise ImportError(
                "tree-sitter and tree-sitter-languages are required. "
                "Install with: pip install tree-sitter tree-sitter-languages"
            )

        self.options = options
        self._init_promise: Optional[asyncio.Future] = None
        self._parsers_cache: Dict[str, asyncio.Future] = {}
        self._languages_cache: Dict[str, Language] = {}

    async def ready(self) -> None:
        """Initialize tree-sitter if not already done"""
        if self._init_promise:
            return await self._init_promise

        self._init_promise = asyncio.Future()
        try:
            # Tree-sitter Python bindings don't need explicit initialization
            self._init_promise.set_result(None)
        except Exception as e:
            self._init_promise.set_exception(e)
            self._init_promise = None
            raise

        return await self._init_promise

    async def parse(self, language_id: str, input_text: str) -> Tree:
        """Parse input text with the specified language"""
        parser = await self.get_language_parser(language_id)

        # Convert string to bytes if needed
        if isinstance(input_text, str):
            input_bytes = input_text.encode('utf-8')
        else:
            input_bytes = input_text

        return parser.parse(input_bytes)

    async def get_language_parser(self, language_id: str) -> Parser:
        """Get or create a parser for the specified language"""
        return await self.create_language_parser(language_id)

    async def get_language(self, language_id: str) -> Language:
        """Get the language object for the specified language"""
        if language_id in self._languages_cache:
            return self._languages_cache[language_id]

        language = await self.load_language(language_id)
        self._languages_cache[language_id] = language
        return language

    async def create_language_parser(self, language_id: str) -> Parser:
        """Create a new parser for the specified language"""
        cache = self._parsers_cache

        if language_id in cache:
            return await cache[language_id]

        parser_future = asyncio.create_task(self._init_language_parser(language_id))
        cache[language_id] = parser_future

        try:
            return await parser_future
        except Exception:
            # Remove from cache on error
            cache.pop(language_id, None)
            raise

    async def _init_language_parser(self, language_id: str) -> Parser:
        """Initialize a parser with the specified language"""
        await self.ready()
        language = await self.load_language(language_id)
        parser = Parser()
        parser.set_language(language)
        return parser

    async def load_language(self, language_id: str) -> Language:
        """Load language from tree-sitter-languages"""
        # Map language IDs to tree-sitter-languages names
        language_map = {
            'csharp': 'c_sharp',
            'cpp': 'cpp',
            'javascript': 'javascript',
            'javascriptreact': 'javascript',  # Use javascript for JSX
            'typescript': 'typescript',
            'typescriptreact': 'tsx',
            'python': 'python',
            'java': 'java',
            'go': 'go',
            'rust': 'rust',
            'c': 'c',
            'php': 'php',
            'kotlin': 'kotlin',
        }

        mapped_language = language_map.get(language_id, language_id)

        try:
            return tree_sitter_languages.get_language(mapped_language)
        except Exception as e:
            raise ValueError(f"Unsupported language: {language_id}") from e

    def dispose(self) -> None:
        """Clean up resources"""
        self._parsers_cache.clear()
        self._languages_cache.clear()
        self._init_promise = None