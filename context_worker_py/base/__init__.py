"""
Base utilities and services
"""

from .common.languages import (
    LanguageIdentifier,
    ILanguageServiceProvider,
    LanguageServiceProvider,
    infer_language,
    is_supported_language
)

from .common.instantiation import (
    ServiceIdentifier,
    InstantiationService,
    provider_container
)

from .common.lifecycle import (
    IDisposable,
    is_disposable
)

from .node.tree_sitter import (
    TreeSitterLoader,
    TreeSitterLoaderOptions
)

__all__ = [
    "LanguageIdentifier",
    "ILanguageServiceProvider",
    "LanguageServiceProvider",
    "infer_language",
    "is_supported_language",
    "ServiceIdentifier",
    "InstantiationService",
    "provider_container",
    "IDisposable",
    "is_disposable",
    "TreeSitterLoader",
    "TreeSitterLoaderOptions",
]