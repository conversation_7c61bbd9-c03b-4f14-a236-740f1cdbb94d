"""
Lifecycle management utilities
"""

from abc import ABC, abstractmethod
from typing import Any


class IDisposable(ABC):
    """Interface for disposable objects"""

    @abstractmethod
    def dispose(self) -> None:
        """Dispose of resources"""
        pass


def is_disposable(obj: Any) -> bool:
    """Check if an object is disposable"""
    return hasattr(obj, 'dispose') and callable(getattr(obj, 'dispose'))