"""
Dependency injection and service instantiation utilities
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Type, Any, Callable, Union, List, Tuple, Dict
from dataclasses import dataclass


T = TypeVar('T')


class ServiceIdentifier(Generic[T]):
    """Service identifier for dependency injection"""

    def __init__(self, name: str):
        self.name = name

    def __str__(self) -> str:
        return self.name

    def __repr__(self) -> str:
        return f"ServiceIdentifier({self.name})"

    def __hash__(self) -> int:
        return hash(self.name)

    def __eq__(self, other) -> bool:
        if isinstance(other, ServiceIdentifier):
            return self.name == other.name
        return False


class LazyServiceIdentifier(Generic[T]):
    """Lazy service identifier for delayed instantiation"""

    def __init__(self, factory: Callable[[], Type[T]]):
        self._factory = factory
        self._unwrapped: Type[T] = None

    def unwrap(self) -> Type[T]:
        """Get the actual service class"""
        if self._unwrapped is None:
            self._unwrapped = self._factory()
        return self._unwrapped


# Type aliases
Newable = Type[T]
FactoryFunction = Callable[[], ServiceIdentifier[T]]
ServiceDescriptor = Union[T, LazyServiceIdentifier[T], FactoryFunction[T]]


class ServicesAccessor(ABC):
    """Interface for accessing services"""

    @abstractmethod
    def get(self, service_id: ServiceIdentifier[T]) -> T:
        """Get service instance by identifier"""
        pass


# Global registry for singleton services
_registry: List[Tuple[ServiceIdentifier, ServiceDescriptor]] = []


def register_singleton(
    service_id: ServiceIdentifier[T],
    descriptor: Union[T, LazyServiceIdentifier[T], Newable[T], FactoryFunction[T]],
    supports_delayed_instantiation: bool = False
) -> None:
    """Register a singleton service"""
    if isinstance(descriptor, LazyServiceIdentifier):
        _registry.append((service_id, descriptor))
        return

    if callable(descriptor) and supports_delayed_instantiation:
        _registry.append((service_id, LazyServiceIdentifier(lambda: descriptor)))
        return

    _registry.append((service_id, descriptor))


def get_singleton_service_descriptors() -> List[Tuple[ServiceIdentifier, ServiceDescriptor]]:
    """Get all registered singleton service descriptors"""
    return _registry.copy()