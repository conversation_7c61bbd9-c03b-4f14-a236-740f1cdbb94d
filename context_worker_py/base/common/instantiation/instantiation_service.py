"""
Service instantiation and dependency injection container
"""

from typing import Dict, Set, Type, Any, Union, TypeVar
from ..lifecycle import is_disposable
from .instantiation import (
    ServiceIdentifier,
    LazyServiceIdentifier,
    get_singleton_service_descriptors,
    Newable
)


T = TypeVar('T')


class ProviderContainer:
    """Simple dependency injection container"""

    def __init__(self):
        self._services: Dict[ServiceIdentifier, Any] = {}
        self._bindings: Dict[ServiceIdentifier, Any] = {}

    def bind(self, service_id: ServiceIdentifier[T]) -> 'ServiceBinding[T]':
        """Bind a service identifier to an implementation"""
        return ServiceBinding(self, service_id)

    def is_bound(self, service_id: ServiceIdentifier[T]) -> bool:
        """Check if a service is bound"""
        return service_id in self._bindings

    def get(self, service_id: ServiceIdentifier[T]) -> T:
        """Get service instance"""
        if service_id in self._services:
            return self._services[service_id]

        if service_id not in self._bindings:
            raise ValueError(f"Service {service_id} is not bound")

        binding = self._bindings[service_id]
        instance = binding.create_instance()

        if binding.is_singleton:
            self._services[service_id] = instance

        return instance

    def unbind_all(self) -> None:
        """Unbind all services"""
        self._services.clear()
        self._bindings.clear()


class ServiceBinding:
    """Service binding configuration"""

    def __init__(self, container: ProviderContainer, service_id: ServiceIdentifier[T]):
        self.container = container
        self.service_id = service_id
        self.is_singleton = False
        self._factory = None
        self._constant_value = None
        self._constructor = None

    def to_constant_value(self, value: T) -> 'ServiceBinding[T]':
        """Bind to a constant value"""
        self._constant_value = value
        self.container._bindings[self.service_id] = self
        return self

    def to_dynamic_value(self, factory: callable) -> 'ServiceBinding[T]':
        """Bind to a dynamic factory function"""
        self._factory = factory
        self.container._bindings[self.service_id] = self
        return self

    def to(self, constructor: Type[T]) -> 'ServiceBinding[T]':
        """Bind to a constructor"""
        self._constructor = constructor
        self.container._bindings[self.service_id] = self
        return self

    def to_self(self) -> 'ServiceBinding[T]':
        """Bind to self (service_id should be a class)"""
        self._constructor = self.service_id
        self.container._bindings[self.service_id] = self
        return self

    def in_singleton_scope(self) -> 'ServiceBinding[T]':
        """Mark as singleton"""
        self.is_singleton = True
        return self

    def create_instance(self) -> T:
        """Create service instance"""
        if self._constant_value is not None:
            return self._constant_value

        if self._factory is not None:
            return self._factory()

        if self._constructor is not None:
            return self._constructor()

        raise ValueError(f"No binding configuration for {self.service_id}")


# Global provider container
provider_container = ProviderContainer()