"""
Language-specific comment and docstring patterns
"""

from typing import Dict, Optional
from dataclasses import dataclass
from .languages import LanguageIdentifier


@dataclass
class BlockComment:
    """Block comment configuration"""
    start: str
    end: str


# Language line comment mapping
LANGUAGE_LINE_COMMENT_MAP: Dict[LanguageIdentifier, str] = {
    'c': '//',
    'cpp': '//',
    'csharp': '//',
    'go': '//',
    'java': '//',
    'python': '#',
    'rust': '//',
    'javascript': '//',
    'typescript': '//',
    'typescriptreact': '//',
}

# Language block comment mapping
LANGUAGE_BLOCK_COMMENT_MAP: Dict[LanguageIdentifier, BlockComment] = {
    'c': BlockComment('/*', '*/'),
    'cpp': BlockComment('/*', '*/'),
    'csharp': BlockComment('/*', '*/'),
    'go': BlockComment('/*', '*/'),
    'java': BlockComment('/*', '*/'),
    'python': BlockComment('"""', '"""'),
    'rust': BlockComment('/*', '*/'),
    'javascript': BlockComment('/*', '*/'),
    'typescript': BlockComment('/*', '*/'),
    'typescriptreact': BlockComment('/*', '*/'),
}


def get_line_comment_prefix(language: LanguageIdentifier) -> Optional[str]:
    """Get line comment prefix for a language"""
    return LANGUAGE_LINE_COMMENT_MAP.get(language)


def get_block_comment_markers(language: LanguageIdentifier) -> Optional[BlockComment]:
    """Get block comment markers for a language"""
    return LANGUAGE_BLOCK_COMMENT_MAP.get(language)