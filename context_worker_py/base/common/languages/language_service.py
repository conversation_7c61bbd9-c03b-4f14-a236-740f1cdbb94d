"""
Language service provider for tree-sitter parsing
"""

import asyncio
from abc import ABC, abstractmethod
from typing import Optional

try:
    from tree_sitter import Language, Parser, Tree
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Parser = None
    Tree = None

from .languages import LanguageIdentifier, is_supported_language
from ...node.tree_sitter.tree_sitter_loader import TreeSitterLoader, TreeSitterLoaderOptions


class ILanguageServiceProvider(ABC):
    """Interface for language service providers"""

    @abstractmethod
    def is_support_language(self, identifier: LanguageIdentifier) -> bool:
        """Check if language is supported"""
        pass

    @abstractmethod
    async def ready(self) -> None:
        """Initialize the service"""
        pass

    @abstractmethod
    async def parse(self, identifier: LanguageIdentifier, input_text: str) -> Optional[Tree]:
        """Parse input text with specified language"""
        pass

    @abstractmethod
    async def get_parser(self, identifier: LanguageIdentifier) -> Optional[Parser]:
        """Get parser for specified language"""
        pass

    @abstractmethod
    async def get_language(self, identifier: LanguageIdentifier) -> Optional[Language]:
        """Get language object for specified language"""
        pass

    @abstractmethod
    def dispose(self) -> None:
        """Clean up resources"""
        pass


class LanguageServiceProvider(ILanguageServiceProvider):
    """Default language service provider implementation"""

    def __init__(self):
        if not HAS_TREE_SITTER:
            raise ImportError(
                "tree-sitter is required. Install with: pip install tree-sitter tree-sitter-languages"
            )

        # Create loader with default file reading capability
        options = TreeSitterLoaderOptions(
            read_file=self._read_file
        )
        self._loader = TreeSitterLoader(options)

    async def _read_file(self, path: str) -> bytes:
        """Default file reading implementation"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._sync_read_file, path)

    def _sync_read_file(self, path: str) -> bytes:
        """Synchronous file reading"""
        with open(path, 'rb') as f:
            return f.read()

    async def ready(self) -> None:
        """Initialize the service"""
        await self._loader.ready()

    async def parse(self, identifier: LanguageIdentifier, input_text: str) -> Optional[Tree]:
        """Parse input text with specified language"""
        if not self.is_support_language(identifier):
            return None

        return await self._loader.parse(identifier, input_text)

    async def get_parser(self, identifier: LanguageIdentifier) -> Optional[Parser]:
        """Get parser for specified language"""
        if self.is_support_language(identifier):
            return await self._loader.get_language_parser(identifier)

        return None

    async def get_language(self, identifier: LanguageIdentifier) -> Optional[Language]:
        """Get language object for specified language"""
        parser = await self.get_parser(identifier)
        if parser:
            return parser.language
        return None

    def is_support_language(self, identifier: LanguageIdentifier) -> bool:
        """Check if language is supported"""
        return is_supported_language(identifier)

    def dispose(self) -> None:
        """Clean up resources"""
        self._loader.dispose()