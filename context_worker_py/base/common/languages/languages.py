"""
Language identification and support utilities
"""

import os
from typing import List, Dict, Optional, Union
from dataclasses import dataclass


# Supported language types
SupportedLanguage = Union[
    'c', 'cpp', 'csharp', 'go', 'java', 'kotlin', 'python', 'php', 'rust',
    'javascript', 'javascriptreact', 'typescript', 'typescriptreact'
]

LanguageIdentifier = Union[SupportedLanguage, str]

SUPPORTED_LANGUAGES: List[LanguageIdentifier] = [
    'c',
    'cpp',
    'csharp',
    'go',
    'java',
    'kotlin',
    'python',
    'kt',
    'rust',
    'php',
    'javascript',
    'javascriptreact',
    'typescript',
    'typescriptreact',
]


def is_supported_language(lang: LanguageIdentifier) -> bool:
    """Check if a language is supported"""
    return lang in SUPPORTED_LANGUAGES


@dataclass
class LanguageItem:
    """Language configuration item"""
    language_id: LanguageIdentifier
    families: Optional[List[str]] = None
    file_exts: List[str] = None

    def __post_init__(self):
        if self.file_exts is None:
            self.file_exts = []
        if self.families is None:
            self.families = []


# Language configuration list
SUPPORT_LANGUAGES_LIST: List[LanguageItem] = [
    LanguageItem('c', ['c'], ['.c', '.h']),
    LanguageItem('cpp', ['cpp', 'c++'], ['.cpp', '.cxx', '.cc', '.hpp', '.hxx', '.hh']),
    LanguageItem('csharp', ['csharp', 'c#'], ['.cs']),
    LanguageItem('go', ['go'], ['.go']),
    LanguageItem('java', ['java'], ['.java']),
    LanguageItem('kotlin', ['kotlin'], ['.kt', '.kts']),
    LanguageItem('python', ['python'], ['.py', '.pyx', '.pyi']),
    LanguageItem('php', ['php'], ['.php']),
    LanguageItem('rust', ['rust'], ['.rs']),
    LanguageItem('javascript', ['javascript', 'js'], ['.js', '.mjs']),
    LanguageItem('javascriptreact', ['javascriptreact', 'jsx'], ['.jsx']),
    LanguageItem('typescript', ['typescript', 'ts'], ['.ts']),
    LanguageItem('typescriptreact', ['typescriptreact', 'tsx'], ['.tsx']),
]


# Build extension to language mapping
_exts_to_language: Dict[str, LanguageIdentifier] = {}
for item in SUPPORT_LANGUAGES_LIST:
    for ext in item.file_exts:
        _exts_to_language[ext.lower()] = item.language_id


def infer_language(filename: str) -> str:
    """
    Infer the language of a file based on its filename.

    Args:
        filename: The filename to infer the language from.

    Returns:
        The inferred language or empty string if the language could not be inferred.
    """
    ext_name = os.path.splitext(filename)[1]
    language_id = _exts_to_language.get(ext_name.lower())

    if not language_id:
        return ''

    return language_id