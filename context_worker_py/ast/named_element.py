"""
Named element representation for AST elements
"""

from typing import Optional, TYPE_CHECKING
from dataclasses import dataclass

from .text_in_range import TextInRange

if TYPE_CHECKING:
    from ..codemodel.code_element_type import CodeElementType
    from .tree_sitter_file import TreeSitterFile


@dataclass
class NamedElement:
    """
    Represents a named element block for AST element.
    For example, in Java, a named element block could be a class or a method.

    Example:
    ```java
    class HelloWorld {
      // This is a method comment
      public static void main(string[] args) {
          System.Out.Println("Hello " + args[0]);
      }
    }
    ```

    In the above example, if we present the `main` method as a named element block:
    - block_range: `public static void main(string[] args) { ... }`
    - identifier_range: `main`
    - code_element_type: `CodeElementType.Method`
    - block_content: `public static void main(string[] args) { ... }`
    - comment_range: `// This is a method comment`
    """

    block_range: TextInRange
    identifier_range: TextInRange
    code_element_type: 'CodeElementType'
    block_content: str
    file: 'TreeSitterFile'
    comment_range: Optional[TextInRange] = None

    def is_test_file(self) -> bool:
        """Check if this element is in a test file"""
        return self.file.is_test_file()

    def get_name(self) -> str:
        """Get the name of this element from the identifier range"""
        return self.identifier_range.text

    def get_full_text(self) -> str:
        """Get the full text content of this element"""
        return self.block_content

    def has_comment(self) -> bool:
        """Check if this element has an associated comment"""
        return self.comment_range is not None

    def get_comment_text(self) -> str:
        """Get the comment text if available"""
        if self.comment_range:
            return self.comment_range.text
        return ""

    def __str__(self) -> str:
        return f"NamedElement({self.code_element_type}, {self.get_name()}, {self.block_range})"

    def __repr__(self) -> str:
        return self.__str__()