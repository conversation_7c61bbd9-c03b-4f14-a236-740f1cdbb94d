"""
Tree-sitter wrapper utilities
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .tree_sitter_file import TreeSitterFile
    from ..base.common.languages.language_service import ILanguageServiceProvider


async def text_to_tree_sitter_file(
    src: str,
    lang_id: str,
    language_service: 'ILanguageServiceProvider'
) -> 'TreeSitterFile':
    """
    Convert text source code to TreeSitterFile

    This is a convenience function for creating TreeSitterFile instances
    from source code text.

    Args:
        src: Source code content
        lang_id: Language identifier
        language_service: Language service provider

    Returns:
        TreeSitterFile instance
    """
    from .tree_sitter_file import TreeSitterFile
    return await TreeSitterFile.create(src, lang_id, language_service)