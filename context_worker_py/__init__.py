"""
Context Worker Python Implementation

This package is a Python port of the TypeScript @autodev/context-worker library.
It provides code analysis and context extraction functionality for various programming languages.

Main components:
- Code analyzers for symbol extraction and interface analysis
- AST processing using tree-sitter
- Language service providers
- File system scanning and code collection
- Context providers for different programming languages
"""

__version__ = "0.1.0"
__author__ = "AutoDev Team"
__license__ = "MIT"

# Core exports from implemented modules
from .types import AppConfig, AnalysisTypes, DEFAULT_CONFIG
from .base import (
    LanguageIdentifier,
    ILanguageServiceProvider,
    LanguageServiceProvider,
    infer_language,
    is_supported_language,
    ServiceIdentifier,
    InstantiationService,
    provider_container
)
from .ast import (
    TextInRange,
    NamedElement,
    TreeSitterUtil,
    TreeSitterFile,
    TreeSitterFileError,
    NamedElementBuilder,
    text_to_tree_sitter_file
)
from .codemodel import (
    CodeElementType,
    CodePosition,
    PositionElement,
    StructureType,
    CodeElement,
    CodeAnnotation,
    CodeParameter,
    CodeVariable,
    CodeFunction,
    CodeStructure,
    CodeFile
)
from .analyzer import (
    CodeAnalysisResult,
    SymbolAnalysisResult,
    SymbolInfo,
    FileSymbols,
    FileSystemScanner,
    CodeCollector,
    ICodeAnalyzer,
    InterfaceAnalyzer,
    SymbolAnalyser
)
from .code_context import (
    LanguageProfile,
    MemoizedQuery,
    LanguageProfileUtil,
    StructurerProvider,
    BaseStructurerProvider,
    PythonProfile,
    TypeScriptProfile,
    JavaProfile
)
from .code_search import (
    ScopeGraph,
    ScopeBuilder,
    TextRange,
    Point,
    SymbolId,
    Symbol,
    LocalScope,
    LocalDef,
    LocalImport,
    Reference
)
from .document import (
    DocumentAnalyser,
    CodeDocument,
    MarkdownAnalyser,
    ReSTAnalyser,
    StreamingMarkdownCodeBlock,
    MarkdownCodeBlock
)
from .utils import (
    to_posix_path,
    are_paths_equal,
    get_readable_path,
    is_larger_than_500kb,
    file_exists_sync,
    safe_read_file_sync,
    capitalize,
    truncate_string,
    generate_id,
    create_progress_bar
)

__all__ = [
    # Configuration
    "AppConfig",
    "AnalysisTypes",
    "DEFAULT_CONFIG",

    # Base utilities
    "LanguageIdentifier",
    "ILanguageServiceProvider",
    "LanguageServiceProvider",
    "infer_language",
    "is_supported_language",
    "ServiceIdentifier",
    "InstantiationService",
    "provider_container",

    # AST processing
    "TextInRange",
    "NamedElement",
    "TreeSitterUtil",
    "TreeSitterFile",
    "TreeSitterFileError",
    "NamedElementBuilder",
    "text_to_tree_sitter_file",

    # Code model
    "CodeElementType",
    "CodePosition",
    "PositionElement",
    "StructureType",
    "CodeElement",
    "CodeAnnotation",
    "CodeParameter",
    "CodeVariable",
    "CodeFunction",
    "CodeStructure",
    "CodeFile",

    # Analyzer
    "CodeAnalysisResult",
    "SymbolAnalysisResult",
    "SymbolInfo",
    "FileSymbols",
    "FileSystemScanner",
    "CodeCollector",
    "ICodeAnalyzer",
    "InterfaceAnalyzer",
    "SymbolAnalyser",

    # Code Context
    "LanguageProfile",
    "MemoizedQuery",
    "LanguageProfileUtil",
    "StructurerProvider",
    "BaseStructurerProvider",
    "PythonProfile",
    "TypeScriptProfile",
    "JavaProfile",

    # Code Search
    "ScopeGraph",
    "ScopeBuilder",
    "TextRange",
    "Point",
    "SymbolId",
    "Symbol",
    "LocalScope",
    "LocalDef",
    "LocalImport",
    "Reference",

    # Document Processing
    "DocumentAnalyser",
    "CodeDocument",
    "MarkdownAnalyser",
    "ReSTAnalyser",
    "StreamingMarkdownCodeBlock",
    "MarkdownCodeBlock",

    # Utilities
    "to_posix_path",
    "are_paths_equal",
    "get_readable_path",
    "is_larger_than_500kb",
    "file_exists_sync",
    "safe_read_file_sync",
    "capitalize",
    "truncate_string",
    "generate_id",
    "create_progress_bar",
]