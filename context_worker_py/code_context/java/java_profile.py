"""
Java language profile implementation
"""

from typing import Optional, TYPE_CHECKING

try:
    from tree_sitter import Language
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None

from ..base.language_profile import LanguageProfile, MemoizedQuery

if TYPE_CHECKING:
    from ...base.common.languages.language_service import ILanguageServiceProvider
    from ...base.common.languages.languages import LanguageIdentifier


class JavaProfile(LanguageProfile):
    """Java language profile for tree-sitter analysis"""

    def __init__(self):
        self.language_ids = ['java']
        self.file_extensions = ['java']

        self.hoverable_query = MemoizedQuery("""
            [(identifier)
             (type_identifier)] @hoverable
        """)

        self.package_query = MemoizedQuery("""
            (package_declaration
              (scoped_identifier) @package.name) @package.declaration
        """)

        self.class_query = MemoizedQuery("""
            [
              (class_declaration
                name: (identifier) @name.definition.class) @definition.class
              (interface_declaration
                name: (identifier) @name.definition.interface) @definition.interface
              (enum_declaration
                name: (identifier) @name.definition.enum) @definition.enum
            ]
        """)

        self.method_query = MemoizedQuery("""
            (method_declaration
              name: (identifier) @name.definition.method) @definition.method
        """)

        self.block_comment_query = MemoizedQuery("""
            [
              (block_comment) @docComment
              (line_comment) @docComment
            ]
        """)

        self.field_query = MemoizedQuery("""
            (field_declaration
              declarator: (variable_declarator
                name: (identifier) @name.definition.field)) @definition.field
        """)

        self.structure_query = MemoizedQuery("""
            [
              (class_declaration) @class
              (interface_declaration) @interface
              (enum_declaration) @enum
              (method_declaration) @method
              (field_declaration) @field
            ]
        """)

        self.symbol_extractor = MemoizedQuery("""
            [
              (class_declaration
                name: (identifier) @class.name) @class.definition
              (interface_declaration
                name: (identifier) @interface.name) @interface.definition
              (enum_declaration
                name: (identifier) @enum.name) @enum.definition
              (method_declaration
                name: (identifier) @method.name) @method.definition
              (field_declaration
                declarator: (variable_declarator
                  name: (identifier) @field.name)) @field.definition
            ]
        """)

        self.scope_query = MemoizedQuery("""
            [
              (program) @scope
              (class_declaration) @scope
              (interface_declaration) @scope
              (method_declaration) @scope
              (block) @scope
            ]
        """)

        self.auto_select_inside_parent = []

        self.built_in_types = [
            'byte', 'short', 'int', 'long', 'float', 'double', 'boolean', 'char',
            'String', 'Object', 'Class', 'Byte', 'Short', 'Integer', 'Long',
            'Float', 'Double', 'Boolean', 'Character', 'Number', 'Void',
            'System', 'Math', 'Thread', 'Runnable', 'Exception', 'Error',
            'RuntimeException', 'Throwable', 'Collection', 'List', 'Set',
            'Map', 'ArrayList', 'LinkedList', 'HashMap', 'HashSet',
            'TreeMap', 'TreeSet', 'Vector', 'Stack', 'Queue', 'Deque',
            'Iterator', 'Enumeration', 'Comparable', 'Comparator',
            'Serializable', 'Cloneable', 'StringBuilder', 'StringBuffer',
            'Date', 'Calendar', 'TimeZone', 'Locale', 'Random', 'Scanner',
            'File', 'InputStream', 'OutputStream', 'Reader', 'Writer',
            'BufferedReader', 'BufferedWriter', 'FileReader', 'FileWriter',
            'PrintWriter', 'PrintStream', 'URL', 'URI', 'Pattern', 'Matcher'
        ]

    async def grammar(self, lang_service: 'ILanguageServiceProvider',
                     lang_id: Optional['LanguageIdentifier'] = None) -> Optional['Language']:
        """Get Java tree-sitter grammar"""
        return await lang_service.get_language('java')

    def is_test_file(self, file_path: str) -> bool:
        """Check if the file is a Java test file"""
        return (
            file_path.endswith('Test.java') or
            file_path.endswith('Tests.java') or
            file_path.endswith('TestCase.java') or
            '/test/' in file_path or
            '/tests/' in file_path or
            file_path.startswith('Test') or
            'test' in file_path.lower()
        )