"""
Language profile registry for managing language configurations
"""

from .base.language_profile_util import LanguageProfileUtil
from .python.python_profile import PythonProfile
from .typescript.typescript_profile import TypeScriptProfile
from .java.java_profile import JavaProfile


def register_all_language_profiles() -> None:
    """Register all available language profiles"""

    # Register Python profile
    LanguageProfileUtil.register_profile(PythonProfile())

    # Register TypeScript profile
    LanguageProfileUtil.register_profile(TypeScriptProfile())

    # Register Java profile
    LanguageProfileUtil.register_profile(JavaProfile())

    # TODO: Add more language profiles as they are implemented
    # LanguageProfileUtil.register_profile(JavaScriptProfile())
    # LanguageProfileUtil.register_profile(GoProfile())
    # LanguageProfileUtil.register_profile(RustProfile())
    # LanguageProfileUtil.register_profile(CSharpProfile())
    # LanguageProfileUtil.register_profile(CppProfile())
    # LanguageProfileUtil.register_profile(CProfile())
    # LanguageProfileUtil.register_profile(KotlinProfile())
    # LanguageProfileUtil.register_profile(PHPProfile())


# Auto-register profiles when module is imported
register_all_language_profiles()