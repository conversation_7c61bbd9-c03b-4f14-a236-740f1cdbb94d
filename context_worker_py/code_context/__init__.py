"""
Code context and language support
"""

from .base import (
    LanguageProfile,
    MemoizedQuery,
    LanguageProfileUtil,
    StructurerProvider,
    BaseStructurerProvider
)

from .python.python_profile import PythonProfile
from .typescript.typescript_profile import TypeScriptProfile
from .java.java_profile import JavaProfile

# Import registry to auto-register profiles
from . import language_profile_registry

__all__ = [
    "LanguageProfile",
    "MemoizedQuery",
    "LanguageProfileUtil",
    "StructurerProvider",
    "BaseStructurerProvider",
    "PythonProfile",
    "TypeScriptProfile",
    "JavaProfile",
]