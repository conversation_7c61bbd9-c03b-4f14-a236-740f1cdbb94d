"""
Structurer provider for parsing source code to structure data
"""

from abc import ABC, abstractmethod
from typing import Optional, List, TYPE_CHECKING
from dataclasses import dataclass

try:
    from tree_sitter import Language, Parser, Query, Node
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Parser = None
    Query = None
    Node = None

if TYPE_CHECKING:
    from ...base.common.languages.language_service import ILanguageServiceProvider
    from ...base.common.languages.languages import LanguageIdentifier
    from ...codemodel.code_element import CodeFile, CodeFunction, CodeStructure, CodeVariable
    from ...codemodel.position_element import PositionElement
    from .language_profile import LanguageProfile


class StructurerProvider(ABC):
    """
    Code structure analysis provider

    Parse source code to structure data, see parse_file method.
    Structure data for CodeElement which can be CodeFile, CodeFunction, CodeVariable
    """

    @abstractmethod
    async def init(self, lang_service: 'ILanguageServiceProvider') -> Optional['Query']:
        """Initialize the structurer provider with language service"""
        pass

    @abstractmethod
    def is_applicable(self, lang: 'LanguageIdentifier') -> bool:
        """Check if this provider is applicable for the given language"""
        pass

    @abstractmethod
    async def parse_file(self, code: str, path: str) -> Optional['CodeFile']:
        """Parse the given code and return a CodeFile object"""
        pass


class BaseStructurerProvider(StructurerProvider):
    """Base implementation of StructurerProvider"""

    def __init__(self):
        self.lang_id: Optional['LanguageIdentifier'] = None
        self.config: Optional['LanguageProfile'] = None
        self.parser: Optional['Parser'] = None
        self.language: Optional['Language'] = None

    async def init(self, lang_service: 'ILanguageServiceProvider') -> Optional['Query']:
        """Initialize the structurer provider with language service"""
        if not self.lang_id:
            return None

        from .language_profile_util import LanguageProfileUtil

        ts_config = LanguageProfileUtil.from_id(self.lang_id)
        if not ts_config:
            return None

        parser = await lang_service.get_parser(self.lang_id)
        if not parser:
            return None

        language = await ts_config.grammar(lang_service, self.lang_id)
        if not language:
            return None

        parser.set_language(language)
        self.parser = parser
        self.language = language
        self.config = ts_config

        return ts_config.structure_query.query(language)

    def insert_location(self, node: 'Node', model: 'PositionElement') -> None:
        """Insert position information from node to model"""
        if not HAS_TREE_SITTER or not node:
            return

        from ...codemodel.position_element import CodePosition

        model.start = CodePosition(node.start_point[0], node.start_point[1])
        model.end = CodePosition(node.end_point[0], node.end_point[1])

    def create_function(self, node: 'Node', name: str) -> 'CodeFunction':
        """Create a CodeFunction from a tree-sitter node"""
        from ...codemodel.code_element import CodeFunction
        from ...codemodel.position_element import CodePosition

        function = CodeFunction(
            name=name,
            vars=[],
            start=CodePosition(0, 0),
            end=CodePosition(0, 0)
        )

        if HAS_TREE_SITTER and node:
            function.start = CodePosition(node.start_point[0], node.start_point[1])
            function.end = CodePosition(node.end_point[0], node.end_point[1])

        return function

    def create_variable(self, node: 'Node', text: str, typ: str) -> 'CodeVariable':
        """Create a CodeVariable from a tree-sitter node"""
        from ...codemodel.code_element import CodeVariable
        from ...codemodel.position_element import CodePosition

        variable = CodeVariable(
            name=text,
            type=typ,
            start=CodePosition(0, 0),
            end=CodePosition(0, 0)
        )

        if HAS_TREE_SITTER and node:
            variable.start = CodePosition(node.start_point[0], node.start_point[1])
            variable.end = CodePosition(node.end_point[0], node.end_point[1])

        return variable

    def init_variable(self) -> 'CodeVariable':
        """Initialize an empty CodeVariable"""
        from ...codemodel.code_element import CodeVariable
        from ...codemodel.position_element import CodePosition

        return CodeVariable(
            name='',
            type='',
            start=CodePosition(0, 0),
            end=CodePosition(0, 0)
        )

    def combine_similar_classes(self, code_file: 'CodeFile') -> None:
        """
        Combine classes with the same name in a given code file.
        Merges the methods and fields of classes with the same name into a single class.
        """
        from ...codemodel.code_element import CodeStructure

        class_map = {}

        for class_item in code_file.classes:
            if class_item.name in class_map:
                # Merge with existing class
                old_class = class_map[class_item.name]
                old_class.methods.extend(class_item.methods)

                if class_item.fields:
                    if old_class.fields:
                        old_class.fields.extend(class_item.fields)
                    else:
                        old_class.fields = class_item.fields

                if class_item.classes:
                    if old_class.classes:
                        old_class.classes.extend(class_item.classes)
                    else:
                        old_class.classes = class_item.classes
            else:
                class_map[class_item.name] = class_item

        # Update the code file with combined classes
        code_file.classes = list(class_map.values())

    def get_node_text(self, node: 'Node') -> str:
        """Get text content of a tree-sitter node"""
        if not HAS_TREE_SITTER or not node:
            return ""

        try:
            if hasattr(node, 'text'):
                return node.text.decode('utf-8')
            return ""
        except (UnicodeDecodeError, AttributeError):
            return ""

    def find_child_by_type(self, node: 'Node', node_type: str) -> Optional['Node']:
        """Find first child node of specified type"""
        if not HAS_TREE_SITTER or not node:
            return None

        for child in node.children:
            if child.type == node_type:
                return child
        return None

    def find_children_by_type(self, node: 'Node', node_type: str) -> List['Node']:
        """Find all child nodes of specified type"""
        if not HAS_TREE_SITTER or not node:
            return []

        return [child for child in node.children if child.type == node_type]