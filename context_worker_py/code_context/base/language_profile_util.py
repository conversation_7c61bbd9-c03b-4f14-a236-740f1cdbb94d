"""
Utility class for working with language profiles
"""

from typing import Optional, List, TYPE_CHECKING
from .language_profile import LanguageProfile

if TYPE_CHECKING:
    from ...base.common.languages.languages import LanguageIdentifier


class LanguageProfileUtil:
    """Utility class for working with tree-sitter languages"""

    # Registry of language profiles
    _language_profiles: List[LanguageProfile] = []

    @classmethod
    def register_profile(cls, profile: LanguageProfile) -> None:
        """Register a language profile"""
        cls._language_profiles.append(profile)

    @classmethod
    def from_id(cls, lang_id: 'LanguageIdentifier') -> Optional[LanguageProfile]:
        """Get language profile by language ID"""
        for profile in cls._language_profiles:
            if any(id.lower() == lang_id.lower() for id in profile.language_ids):
                return profile
        return None

    @classmethod
    def get_all_profiles(cls) -> List[LanguageProfile]:
        """Get all registered language profiles"""
        return cls._language_profiles.copy()

    @classmethod
    def clear_profiles(cls) -> None:
        """Clear all registered profiles (mainly for testing)"""
        cls._language_profiles.clear()

    @classmethod
    def get_supported_languages(cls) -> List[str]:
        """Get list of all supported language IDs"""
        languages = []
        for profile in cls._language_profiles:
            languages.extend(profile.language_ids)
        return languages

    @classmethod
    def get_supported_extensions(cls) -> List[str]:
        """Get list of all supported file extensions"""
        extensions = []
        for profile in cls._language_profiles:
            extensions.extend(profile.file_extensions)
        return list(set(extensions))  # Remove duplicates