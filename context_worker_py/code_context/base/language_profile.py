"""
Language profile interface and memoized query implementation
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Callable, Awaitable, TYPE_CHECKING
from dataclasses import dataclass

try:
    from tree_sitter import Language, Query
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None
    Query = None

if TYPE_CHECKING:
    from ...base.common.languages.language_service import ILanguageServiceProvider
    from ...base.common.languages.languages import LanguageIdentifier


class MemoizedQuery:
    """Memoized tree-sitter query for performance optimization"""

    def __init__(self, query_string: str):
        self.query_string = query_string
        self._query_cache: dict = {}

    def query(self, language: 'Language') -> Optional['Query']:
        """Get or create a query for the given language"""
        if not HAS_TREE_SITTER or not language:
            return None

        # Use language object as cache key
        lang_key = id(language)

        if lang_key not in self._query_cache:
            try:
                self._query_cache[lang_key] = language.query(self.query_string)
            except Exception as e:
                print(f"Error creating query: {e}")
                return None

        return self._query_cache[lang_key]


class LanguageProfile(ABC):
    """
    Language profile interface that defines the structure of a language profile object.
    This object contains information about a specific programming language that is used
    for code analysis and processing.
    """

    # A list of language names that can be processed by these node queries
    # e.g.: ["typescript", "typescriptreact"], ["rust"]
    language_ids: List[str]

    # Extensions that can help classify the file: .rs, .rb, .cabal
    file_extensions: List[str]

    # tree-sitter grammar for this language
    @abstractmethod
    async def grammar(self, lang_service: 'ILanguageServiceProvider',
                     lang_id: Optional['LanguageIdentifier'] = None) -> Optional['Language']:
        """Get tree-sitter grammar for this language"""
        pass

    # Compiled tree-sitter hoverables query
    hoverable_query: MemoizedQuery

    # in java, the canonical name is the package name
    package_query: Optional[MemoizedQuery] = None

    # class query
    class_query: MemoizedQuery

    # method query
    method_query: MemoizedQuery

    # function query (for languages that have standalone functions)
    function_query: Optional[MemoizedQuery] = None

    block_comment_query: MemoizedQuery

    # method input and output query
    method_io_query: Optional[MemoizedQuery] = None

    field_query: Optional[MemoizedQuery] = None

    # structurer query
    structure_query: MemoizedQuery

    # For extract core information from the code
    symbol_extractor: MemoizedQuery

    # scope query for scope graph analysis
    scope_query: Optional[MemoizedQuery] = None

    # should select parent
    # for example, in JavaScript/TypeScript, if we select function, we should also select the export keyword.
    auto_select_inside_parent: List[str]

    # for IO analysis
    built_in_types: List[str]

    # should return true if the file is a test file
    @abstractmethod
    def is_test_file(self, file_path: str) -> bool:
        """Check if the file is a test file"""
        pass