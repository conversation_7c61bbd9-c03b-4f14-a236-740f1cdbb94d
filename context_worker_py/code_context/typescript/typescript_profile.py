"""
TypeScript language profile implementation
"""

from typing import Optional, TYPE_CHECKING

try:
    from tree_sitter import Language
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None

from ..base.language_profile import LanguageProfile, MemoizedQuery

if TYPE_CHECKING:
    from ...base.common.languages.language_service import ILanguageServiceProvider
    from ...base.common.languages.languages import LanguageIdentifier


class TypeScriptProfile(LanguageProfile):
    """TypeScript language profile for tree-sitter analysis"""

    def __init__(self):
        self.language_ids = ['typescript', 'typescriptreact']
        self.file_extensions = ['ts', 'tsx']

        self.hoverable_query = MemoizedQuery("""
            [(identifier)
             (property_identifier)
             (type_identifier)] @hoverable
        """)

        self.class_query = MemoizedQuery("""
            (class_declaration
              name: (type_identifier) @name.definition.class) @definition.class
        """)

        self.method_query = MemoizedQuery("""
            [
              (method_definition
                name: (property_identifier) @name.definition.method) @definition.method
              (function_declaration
                name: (identifier) @name.definition.method) @definition.method
            ]
        """)

        self.function_query = MemoizedQuery("""
            [
              (function_declaration
                name: (identifier) @name.definition.function) @definition.function
              (arrow_function) @definition.function
            ]
        """)

        self.block_comment_query = MemoizedQuery("""
            (comment) @docComment
        """)

        self.structure_query = MemoizedQuery("""
            [
              (class_declaration) @class
              (interface_declaration) @interface
              (function_declaration) @function
              (method_definition) @method
            ]
        """)

        self.symbol_extractor = MemoizedQuery("""
            [
              (class_declaration
                name: (type_identifier) @class.name) @class.definition
              (interface_declaration
                name: (type_identifier) @interface.name) @interface.definition
              (function_declaration
                name: (identifier) @function.name) @function.definition
              (method_definition
                name: (property_identifier) @method.name) @method.definition
              (variable_declarator
                name: (identifier) @variable.name) @variable.definition
            ]
        """)

        self.scope_query = MemoizedQuery("""
            [
              (program) @scope
              (class_declaration) @scope
              (interface_declaration) @scope
              (function_declaration) @scope
              (method_definition) @scope
              (arrow_function) @scope
            ]
        """)

        self.auto_select_inside_parent = ['export_statement']

        self.built_in_types = [
            'string', 'number', 'boolean', 'object', 'undefined', 'null',
            'any', 'unknown', 'never', 'void', 'bigint', 'symbol',
            'Array', 'Object', 'Function', 'String', 'Number', 'Boolean',
            'Date', 'RegExp', 'Error', 'Promise', 'Map', 'Set', 'WeakMap',
            'WeakSet', 'ArrayBuffer', 'DataView', 'Int8Array', 'Uint8Array',
            'Uint8ClampedArray', 'Int16Array', 'Uint16Array', 'Int32Array',
            'Uint32Array', 'Float32Array', 'Float64Array', 'BigInt64Array',
            'BigUint64Array', 'JSON', 'Math', 'console', 'window', 'document',
            'HTMLElement', 'Element', 'Node', 'EventTarget'
        ]

    async def grammar(self, lang_service: 'ILanguageServiceProvider',
                     lang_id: Optional['LanguageIdentifier'] = None) -> Optional['Language']:
        """Get TypeScript tree-sitter grammar"""
        if lang_id == 'typescriptreact':
            return await lang_service.get_language('typescriptreact')
        return await lang_service.get_language('typescript')

    def is_test_file(self, file_path: str) -> bool:
        """Check if the file is a TypeScript test file"""
        return (
            file_path.endswith('.test.ts') or
            file_path.endswith('.test.tsx') or
            file_path.endswith('.spec.ts') or
            file_path.endswith('.spec.tsx') or
            '/test/' in file_path or
            '/tests/' in file_path or
            '/__tests__/' in file_path
        )