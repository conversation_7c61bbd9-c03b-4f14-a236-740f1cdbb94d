"""
Python language profile implementation
"""

from typing import Optional, TYPE_CHECKING

try:
    from tree_sitter import Language
    HAS_TREE_SITTER = True
except ImportError:
    HAS_TREE_SITTER = False
    Language = None

from ..base.language_profile import LanguageProfile, MemoizedQuery

if TYPE_CHECKING:
    from ...base.common.languages.language_service import ILanguageServiceProvider
    from ...base.common.languages.languages import LanguageIdentifier


class PythonProfile(LanguageProfile):
    """Python language profile for tree-sitter analysis"""

    def __init__(self):
        self.language_ids = ['python']
        self.file_extensions = ['py', 'pyx', 'pyi']

        self.hoverable_query = MemoizedQuery("""
            [(identifier)
             (attribute)] @hoverable
        """)

        self.class_query = MemoizedQuery("""
            (class_definition
              name: (identifier) @name.definition.class) @definition.class
        """)

        self.method_query = MemoizedQuery("""
            (function_definition
              name: (identifier) @name.definition.method) @definition.method
        """)

        self.function_query = MemoizedQuery("""
            (function_definition
              name: (identifier) @name.definition.function) @definition.function
        """)

        self.block_comment_query = MemoizedQuery("""
            (comment) @docComment
        """)

        self.structure_query = MemoizedQuery("""
            [
              (class_definition) @class
              (function_definition) @function
            ]
        """)

        self.symbol_extractor = MemoizedQuery("""
            [
              (class_definition
                name: (identifier) @class.name) @class.definition
              (function_definition
                name: (identifier) @function.name) @function.definition
              (assignment
                left: (identifier) @variable.name) @variable.definition
            ]
        """)

        self.scope_query = MemoizedQuery("""
            [
              (module) @scope
              (class_definition) @scope
              (function_definition) @scope
            ]
        """)

        self.auto_select_inside_parent = []

        self.built_in_types = [
            'int', 'float', 'str', 'bool', 'list', 'dict', 'tuple', 'set',
            'None', 'object', 'type', 'bytes', 'bytearray', 'complex',
            'frozenset', 'range', 'slice', 'memoryview', 'property',
            'staticmethod', 'classmethod', 'super', 'enumerate', 'zip',
            'map', 'filter', 'reversed', 'sorted', 'any', 'all',
            'min', 'max', 'sum', 'len', 'abs', 'round', 'pow',
            'divmod', 'isinstance', 'issubclass', 'hasattr', 'getattr',
            'setattr', 'delattr', 'callable', 'iter', 'next',
            'open', 'print', 'input', 'repr', 'str', 'int', 'float',
            'bool', 'list', 'dict', 'tuple', 'set'
        ]

    async def grammar(self, lang_service: 'ILanguageServiceProvider',
                     lang_id: Optional['LanguageIdentifier'] = None) -> Optional['Language']:
        """Get Python tree-sitter grammar"""
        return await lang_service.get_language('python')

    def is_test_file(self, file_path: str) -> bool:
        """Check if the file is a Python test file"""
        return (
            file_path.startswith('test_') or
            file_path.endswith('_test.py') or
            file_path.endswith('_spec.py') or
            '/test/' in file_path or
            '/tests/' in file_path
        )