"""
Scope graph implementation for code analysis
"""

from typing import List, Dict, Set, Optional, Union, TYPE_CHECKING
from dataclasses import dataclass, field

from .model.text_range import TextRange
from .model.symbol_id import SymbolId, NameSpaces
from .model.symbol import Symbol
from .model.import_with_refs import ImportWithRefs
from .node.node_kind import NodeKind
from .node.local_scope import LocalScope
from .node.local_def import LocalDef
from .node.local_import import LocalImport
from .node.reference import Reference
from .edge.edge_kind import EdgeKind, DefToScope, ImportToScope, RefToDef, RefToImport, ScopeToScope

if TYPE_CHECKING:
    pass


# Type aliases
NodeId = int
EdgeId = int


@dataclass
class Edge:
    """Represents an edge in the scope graph"""
    source: NodeId
    target: NodeId
    kind: EdgeKind


@dataclass
class Node:
    """Represents a node in the scope graph"""
    id: NodeId
    kind: NodeKind


class ScopeGraph:
    """
    A scope graph represents the scopes and their relationships in a program.

    This class provides methods to build and query the scope graph, including
    finding definitions, references, and scope relationships.
    """

    def __init__(self):
        self.nodes: Dict[NodeId, Node] = {}
        self.edges: Dict[EdgeId, Edge] = {}
        self.namespaces: NameSpaces = []
        self._next_node_id = 0
        self._next_edge_id = 0

        # Index for efficient lookups
        self._edges_from: Dict[NodeId, List[EdgeId]] = {}
        self._edges_to: Dict[NodeId, List[EdgeId]] = {}

        # Symbol tracking
        self._symbols: List[Symbol] = []
        self._imports: List[ImportWithRefs] = []

    def add_node(self, kind: NodeKind) -> NodeId:
        """Add a node to the scope graph"""
        node_id = self._next_node_id
        self._next_node_id += 1

        node = Node(id=node_id, kind=kind)
        self.nodes[node_id] = node

        # Initialize edge indices
        self._edges_from[node_id] = []
        self._edges_to[node_id] = []

        return node_id

    def add_edge(self, source: NodeId, target: NodeId, kind: EdgeKind) -> EdgeId:
        """Add an edge to the scope graph"""
        edge_id = self._next_edge_id
        self._next_edge_id += 1

        edge = Edge(source=source, target=target, kind=kind)
        self.edges[edge_id] = edge

        # Update indices
        self._edges_from[source].append(edge_id)
        self._edges_to[target].append(edge_id)

        return edge_id

    def get_node(self, node_id: NodeId) -> Optional[Node]:
        """Get a node by its ID"""
        return self.nodes.get(node_id)

    def get_edge(self, edge_id: EdgeId) -> Optional[Edge]:
        """Get an edge by its ID"""
        return self.edges.get(edge_id)

    def edges_from(self, node_id: NodeId) -> List[EdgeId]:
        """Get all edges originating from a node"""
        return self._edges_from.get(node_id, [])

    def edges_to(self, node_id: NodeId) -> List[EdgeId]:
        """Get all edges targeting a node"""
        return self._edges_to.get(node_id, [])

    def find_edges(self, source: Optional[NodeId] = None,
                   target: Optional[NodeId] = None,
                   kind: Optional[type] = None) -> List[EdgeId]:
        """Find edges matching the given criteria"""
        result = []

        for edge_id, edge in self.edges.items():
            if source is not None and edge.source != source:
                continue
            if target is not None and edge.target != target:
                continue
            if kind is not None and not isinstance(edge.kind, kind):
                continue
            result.append(edge_id)

        return result

    def find_nodes(self, kind: Optional[type] = None) -> List[NodeId]:
        """Find nodes matching the given criteria"""
        result = []

        for node_id, node in self.nodes.items():
            if kind is not None and not isinstance(node.kind, kind):
                continue
            result.append(node_id)

        return result

    def add_symbol(self, symbol: Symbol) -> None:
        """Add a symbol to the scope graph"""
        self._symbols.append(symbol)

    def symbols(self) -> List[Symbol]:
        """Get all symbols in the scope graph"""
        return self._symbols.copy()

    def add_import(self, import_ref: ImportWithRefs) -> None:
        """Add an import reference to the scope graph"""
        self._imports.append(import_ref)

    def imports(self) -> List[ImportWithRefs]:
        """Get all import references in the scope graph"""
        return self._imports.copy()

    def find_definitions(self, symbol_id: SymbolId) -> List[NodeId]:
        """Find all definition nodes for a given symbol"""
        result = []

        for node_id, node in self.nodes.items():
            if isinstance(node.kind, LocalDef) and node.kind.symbol_id == symbol_id:
                result.append(node_id)

        return result

    def find_references(self, symbol_id: SymbolId) -> List[NodeId]:
        """Find all reference nodes for a given symbol"""
        result = []

        for node_id, node in self.nodes.items():
            if isinstance(node.kind, Reference) and node.kind.symbol_id == symbol_id:
                result.append(node_id)

        return result

    def find_imports(self, symbol_id: SymbolId) -> List[NodeId]:
        """Find all import nodes for a given symbol"""
        result = []

        for node_id, node in self.nodes.items():
            if isinstance(node.kind, LocalImport) and node.kind.symbol_id == symbol_id:
                result.append(node_id)

        return result

    def find_scopes(self) -> List[NodeId]:
        """Find all scope nodes"""
        return self.find_nodes(LocalScope)

    def get_scope_for_node(self, node_id: NodeId) -> Optional[NodeId]:
        """Get the scope that contains a given node"""
        # Find edges from the node to a scope
        for edge_id in self.edges_from(node_id):
            edge = self.get_edge(edge_id)
            if edge and isinstance(edge.kind, (DefToScope, ImportToScope)):
                target_node = self.get_node(edge.target)
                if target_node and isinstance(target_node.kind, LocalScope):
                    return edge.target

        return None

    def get_parent_scope(self, scope_id: NodeId) -> Optional[NodeId]:
        """Get the parent scope of a given scope"""
        for edge_id in self.edges_from(scope_id):
            edge = self.get_edge(edge_id)
            if edge and isinstance(edge.kind, ScopeToScope):
                return edge.target

        return None

    def get_child_scopes(self, scope_id: NodeId) -> List[NodeId]:
        """Get all child scopes of a given scope"""
        result = []

        for edge_id in self.edges_to(scope_id):
            edge = self.get_edge(edge_id)
            if edge and isinstance(edge.kind, ScopeToScope):
                result.append(edge.source)

        return result

    def resolve_reference(self, ref_node_id: NodeId) -> List[NodeId]:
        """
        Resolve a reference to its possible definitions or imports.

        Returns a list of node IDs that the reference could refer to.
        """
        result = []

        # Find direct edges from reference to definitions or imports
        for edge_id in self.edges_from(ref_node_id):
            edge = self.get_edge(edge_id)
            if edge and isinstance(edge.kind, (RefToDef, RefToImport)):
                result.append(edge.target)

        return result

    def get_statistics(self) -> Dict[str, int]:
        """Get statistics about the scope graph"""
        stats = {
            'total_nodes': len(self.nodes),
            'total_edges': len(self.edges),
            'scopes': len(self.find_scopes()),
            'definitions': len(self.find_nodes(LocalDef)),
            'references': len(self.find_nodes(Reference)),
            'imports': len(self.find_nodes(LocalImport)),
            'symbols': len(self._symbols),
            'import_refs': len(self._imports)
        }

        return stats