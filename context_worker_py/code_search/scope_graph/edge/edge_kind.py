"""
Edge types for scope graph
"""

from abc import ABC


class EdgeKind(ABC):
    """Base class for all edge types in the scope graph"""

    def __str__(self) -> str:
        return self.__class__.__name__

    def __repr__(self) -> str:
        return self.__str__()


class DefToScope(EdgeKind):
    """Edge from definition to scope"""
    pass


class ImportToScope(EdgeKind):
    """Edge from import to scope"""
    pass


class RefToDef(EdgeKind):
    """Edge from reference to definition"""
    pass


class RefToImport(EdgeKind):
    """Edge from reference to import"""
    pass


class ScopeToScope(EdgeKind):
    """Edge from scope to parent scope"""
    pass