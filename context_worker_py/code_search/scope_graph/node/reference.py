"""
Reference node for scope graph
"""

from .node_kind import Node<PERSON>ind
from ..model.text_range import TextRange
from ..model.symbol_id import SymbolId


class Reference(NodeKind):
    """Represents a reference in the scope graph"""

    def __init__(self, range: TextRange, symbol_id: SymbolId):
        super().__init__(range)
        self.symbol_id = symbol_id

    def name(self, source_code: str) -> str:
        """Get the name of this reference from source code"""
        return source_code[self.range.start.byte:self.range.end.byte]