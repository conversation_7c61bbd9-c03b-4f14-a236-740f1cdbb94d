"""
Local import node for scope graph
"""

from .node_kind import <PERSON>de<PERSON>ind
from ..model.text_range import TextRange
from ..model.symbol_id import SymbolId


class LocalImport(NodeKind):
    """Represents a local import in the scope graph"""

    def __init__(self, range: TextRange, symbol_id: SymbolId):
        super().__init__(range)
        self.symbol_id = symbol_id

    def name(self, source_code: str) -> str:
        """Get the name of this import from source code"""
        return source_code[self.range.start.byte:self.range.end.byte]