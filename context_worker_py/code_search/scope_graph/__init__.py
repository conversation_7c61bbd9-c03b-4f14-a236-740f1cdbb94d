"""
Scope graph analysis components
"""

from .scope_graph import <PERSON>opeGrap<PERSON>, Node, Edge, NodeId, EdgeId
from .scope_builder import Scope<PERSON>uilder

from .model import (
    Point,
    TextRange,
    SymbolId,
    NameSpaces,
    Symbol,
    ImportWithRefs,
    name_of_symbol,
    all_symbols,
    symbol_id_of
)

from .node import (
    NodeKind,
    LocalScope,
    ScopeStack,
    LocalDef,
    LocalImport,
    Reference
)

from .edge import (
    EdgeKind,
    DefToScope,
    ImportToScope,
    RefToDef,
    RefToImport,
    ScopeToScope
)

__all__ = [
    # Core classes
    "ScopeGraph",
    "Node",
    "Edge",
    "NodeId",
    "EdgeId",
    "ScopeBuilder",

    # Model components
    "Point",
    "TextRange",
    "SymbolId",
    "NameSpaces",
    "Symbol",
    "ImportWithRefs",
    "name_of_symbol",
    "all_symbols",
    "symbol_id_of",

    # Node types
    "NodeKind",
    "LocalScope",
    "ScopeStack",
    "LocalDef",
    "LocalImport",
    "Reference",

    # Edge types
    "EdgeKind",
    "DefToScope",
    "ImportToScope",
    "RefToDef",
    "RefToImport",
    "ScopeToScope",
]