"""
Code search and scope analysis
"""

from .scope_graph import (
    <PERSON>opeGraph,
    ScopeBuilder,
    Node,
    Edge,
    NodeId,
    EdgeId,
    Point,
    TextRange,
    SymbolId,
    NameSpaces,
    Symbol,
    ImportWithRefs,
    NodeKind,
    LocalScope,
    ScopeStack,
    LocalDef,
    LocalImport,
    Reference,
    EdgeKind,
    DefToScope,
    ImportToScope,
    RefToDef,
    RefToImport,
    ScopeToScope,
    name_of_symbol,
    all_symbols,
    symbol_id_of
)

__all__ = [
    # Core classes
    "ScopeGraph",
    "ScopeBuilder",
    "Node",
    "Edge",
    "NodeId",
    "EdgeId",

    # Model components
    "Point",
    "TextRange",
    "SymbolId",
    "NameSpaces",
    "Symbol",
    "ImportWithRefs",
    "name_of_symbol",
    "all_symbols",
    "symbol_id_of",

    # Node types
    "NodeKind",
    "LocalScope",
    "ScopeStack",
    "LocalDef",
    "LocalImport",
    "Reference",

    # Edge types
    "<PERSON>Kind",
    "DefToScope",
    "ImportToScope",
    "RefToDef",
    "RefToImport",
    "ScopeToScope",
]