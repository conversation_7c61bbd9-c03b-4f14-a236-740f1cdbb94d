# Context Worker Python

Python port of the TypeScript @autodev/context-worker library.

## Overview

This package provides code analysis and context extraction functionality for various programming languages. It's designed to help developers understand and analyze codebases by extracting symbols, interfaces, and other contextual information.

## Features

- **Code Analysis**: Symbol extraction and interface analysis
- **AST Processing**: Tree-sitter based parsing for multiple languages
- **Language Services**: Pluggable language service providers
- **File System Scanning**: Efficient code collection and scanning
- **Context Providers**: Language-specific context extraction

## Installation

```bash
pip install context-worker-py
```

## Usage

```python
from context_worker_py import CodeCollector, SymbolAnalyser

# Initialize code collector
collector = CodeCollector("/path/to/your/project")

# Analyze symbols
analyzer = SymbolAnalyser()
results = analyzer.analyze(collector.collect_files())
```

## Development

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black context_worker_py/
isort context_worker_py/

# Type checking
mypy context_worker_py/
```

## License

MIT License - see LICENSE file for details.